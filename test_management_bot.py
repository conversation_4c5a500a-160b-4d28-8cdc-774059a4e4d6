#!/usr/bin/env python3
"""
Simple test script for the management bot
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing Management Bot Imports...")
    
    try:
        print("  ✓ Testing config import...")
        from src.config import NOTIFICATION_BOT_TOKEN, ORDER_TRACK_BOT_AUTHORIZED_IDS, logger
        print(f"    - Token available: {'Yes' if NOTIFICATION_BOT_TOKEN else 'No'}")
        print(f"    - Authorized IDs: {ORDER_TRACK_BOT_AUTHORIZED_IDS}")
        
        print("  ✓ Testing Firebase import...")
        from src.firebase_db import get_data, set_data
        
        print("  ✓ Testing data models import...")
        from src.data_models import DeliveryPersonnel
        
        print("  ✓ Testing delivery personnel utils import...")
        from src.utils.delivery_personnel_utils import (
            create_delivery_personnel,
            get_delivery_personnel_by_telegram_id,
            get_delivery_personnel_by_id
        )
        
        print("  ✓ Testing telebot import...")
        import telebot
        
        print("  ✓ Testing management bot import...")
        from src.bots.management_bot import management_bot
        
        print("✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_bot_initialization():
    """Test if the management bot can be initialized"""
    print("\n🤖 Testing Bot Initialization...")
    
    try:
        from src.bots.management_bot import management_bot
        
        # Try to get bot info (this will test the token)
        bot_info = management_bot.get_me()
        print(f"  ✓ Bot username: @{bot_info.username}")
        print(f"  ✓ Bot ID: {bot_info.id}")
        print(f"  ✓ Bot name: {bot_info.first_name}")
        
        print("✅ Bot initialization successful!")
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization error: {e}")
        return False

def test_firebase_collections():
    """Test Firebase collections setup"""
    print("\n🔥 Testing Firebase Collections...")
    
    try:
        from src.management_firebase_setup import validate_management_data, initialize_management_collections
        
        # Validate existing collections
        validation = validate_management_data()
        print(f"  ✓ Validation results: {validation}")
        
        if not validation or not all(validation.values()):
            print("  ⚠️ Some collections missing, attempting to initialize...")
            success = initialize_management_collections()
            if success:
                print("  ✓ Collections initialized successfully")
            else:
                print("  ❌ Failed to initialize collections")
                return False
        
        print("✅ Firebase collections ready!")
        return True
        
    except Exception as e:
        print(f"❌ Firebase error: {e}")
        return False

def test_analytics_functions():
    """Test analytics calculation functions"""
    print("\n📊 Testing Analytics Functions...")
    
    try:
        from src.bots.management_bot import calculate_daily_profits, calculate_personnel_earnings
        
        # Test daily profits calculation
        daily_profits = calculate_daily_profits()
        print(f"  ✓ Daily profits calculation: {daily_profits.get('total_orders', 0)} orders found")
        
        # Test personnel earnings calculation
        personnel_earnings = calculate_personnel_earnings()
        print(f"  ✓ Personnel earnings calculation: {len(personnel_earnings)} personnel found")
        
        print("✅ Analytics functions working!")
        return True
        
    except Exception as e:
        print(f"❌ Analytics error: {e}")
        return False

def main():
    """Run all tests"""
    print("🏢 Wiz Aroma Management Bot Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_bot_initialization,
        test_firebase_collections,
        test_analytics_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Management bot is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
