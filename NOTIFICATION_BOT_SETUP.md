# Notification Bot Setup

This document explains how to set up and configure the Notification Bot that sends order details to a specific channel when orders are approved by the finance team.

## Bot Configuration

1. The notification bot has been configured with the following details:
   - Bot Token: `8187447788:AAHh_MU3EPnyCz29aYbnPLERUUkPpxyGlrA`
   - Chat ID: `5546595738`

2. This bot will automatically send order details to the specified chat when an order is approved by the finance team.

## Message Format

The bot sends messages in the following format:

```
ORDER_NUMBER

📱 Phone: CUSTOMER_PHONE

🏪 Restaurant: RESTAURANT_NAME
📍 Delivery to: DELIVERY_LOCATION


📋 ORDER ITEMS:
• Item 1 (x1) - PRICE birr
• Item 2 (x2) - PRICE birr

📝 Special Instructions:
SPECIAL_INSTRUCTIONS
```

## How to Run

To run the notification bot along with the other bots:

```bash
python main.py --bot=all
```

To run only the notification bot:

```bash
python main.py --bot=notification
```

## Environment Configuration

The notification bot uses two environment variables:

1. `NOTIFICATION_BOT_TOKEN`: The Telegram bot token
2. `NOTIFICATION_CHAT_ID`: The chat ID where notifications should be sent

If these are not set in your .env file, the following default values are used:
- Bot Token: `8187447788:AAHh_MU3EPnyCz29aYbnPLERUUkPpxyGlrA`
- Chat ID: `5546595738`

## Testing

To verify the notification bot is working:
1. Process an order through the system
2. Approve the payment via the finance bot
3. Verify that the order details are sent to the notification chat

## Troubleshooting

If you encounter issues with the notification bot:

1. Check that the bot token is valid
2. Ensure the bot has been added to the chat with the specified chat ID
3. Verify the bot has permission to send messages in the chat
4. Check the logs for any error messages related to the notification bot 