#!/usr/bin/env python3
"""
Debug Firebase JWT Token Issues for Wiz-Aroma V-1.3.3

This script helps diagnose JWT token validation issues with Firebase.
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone
import time

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_system_time():
    """Check system time and timezone"""
    print("🕐 System Time Analysis")
    print("=" * 50)
    
    # Local time
    local_time = datetime.now()
    print(f"Local time: {local_time}")
    print(f"Local timezone: {local_time.astimezone().tzinfo}")
    
    # UTC time
    utc_time = datetime.now(timezone.utc)
    print(f"UTC time: {utc_time}")
    
    # Unix timestamp
    unix_timestamp = time.time()
    print(f"Unix timestamp: {unix_timestamp}")
    
    # Check if times are reasonable
    time_diff = abs((utc_time - local_time.replace(tzinfo=timezone.utc)).total_seconds())
    if time_diff > 3600:  # More than 1 hour difference
        print(f"⚠️  WARNING: Large time difference detected: {time_diff} seconds")
    else:
        print(f"✅ Time difference is reasonable: {time_diff} seconds")
    
    return utc_time

def check_credentials_file():
    """Check Firebase credentials file"""
    print("\n🔑 Firebase Credentials Analysis")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    credentials_path = os.getenv('FIREBASE_CREDENTIALS_PATH', 'wiz-aroma-adama-firebase-adminsdk-fbsvc-c2564abcb8.json')
    
    if not os.path.exists(credentials_path):
        print(f"❌ Credentials file not found: {credentials_path}")
        return None
    
    print(f"✅ Credentials file found: {credentials_path}")
    
    try:
        with open(credentials_path, 'r') as f:
            creds_data = json.load(f)
        
        print(f"✅ Credentials file is valid JSON")
        print(f"Project ID: {creds_data.get('project_id', 'unknown')}")
        print(f"Client Email: {creds_data.get('client_email', 'unknown')}")
        print(f"Private Key ID: {creds_data.get('private_key_id', 'unknown')[:20]}...")
        
        # Check if private key exists
        if 'private_key' in creds_data:
            print(f"✅ Private key present (length: {len(creds_data['private_key'])} chars)")
        else:
            print(f"❌ Private key missing")
            return None
            
        return creds_data
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in credentials file: {e}")
        return None
    except Exception as e:
        print(f"❌ Error reading credentials file: {e}")
        return None

def test_firebase_initialization():
    """Test Firebase initialization with detailed error reporting"""
    print("\n🔥 Firebase Initialization Test")
    print("=" * 50)
    
    try:
        import firebase_admin
        from firebase_admin import credentials, db
        
        # Clear any existing apps
        try:
            firebase_admin.delete_app(firebase_admin.get_app())
            print("✅ Cleared existing Firebase app")
        except ValueError:
            print("ℹ️  No existing Firebase app to clear")
        
        # Load credentials
        credentials_path = os.getenv('FIREBASE_CREDENTIALS_PATH', 'wiz-aroma-adama-firebase-adminsdk-fbsvc-c2564abcb8.json')
        database_url = os.getenv('FIREBASE_DATABASE_URL', 'https://wiz-aroma-adama-default-rtdb.firebaseio.com')
        
        print(f"Using credentials: {credentials_path}")
        print(f"Using database URL: {database_url}")
        
        # Create credentials object
        cred = credentials.Certificate(credentials_path)
        print("✅ Credentials object created successfully")
        
        # Initialize Firebase app
        app = firebase_admin.initialize_app(cred, {
            'databaseURL': database_url
        })
        print("✅ Firebase app initialized successfully")
        
        # Get database reference
        db_ref = db.reference()
        print("✅ Database reference obtained")
        
        # Test simple read operation
        print("\n📖 Testing read operation...")
        test_data = db_ref.child('system_health').get()
        print(f"✅ Read operation successful: {test_data}")

        # Test simple write operation
        print("\n📝 Testing write operation...")
        test_write_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "test_status": "success",
            "message": "JWT debug test successful"
        }
        db_ref.child('jwt_debug_test').set(test_write_data)
        print("✅ Write operation successful")

        # Test read back
        print("\n🔄 Testing read-back...")
        read_back = db_ref.child('jwt_debug_test').get()
        if read_back and read_back.get('test_status') == 'success':
            print("✅ Read-back successful - Firebase is working!")
            return True
        else:
            print("❌ Read-back failed")
            return False
        
    except Exception as e:
        print(f"❌ Firebase test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Check for specific JWT errors
        error_str = str(e)
        if 'invalid_grant' in error_str.lower():
            print("🔍 JWT Token Error Detected:")
            print("   - This is likely a time synchronization issue")
            print("   - Or the service account credentials may need regeneration")
        elif 'permission denied' in error_str.lower():
            print("🔍 Permission Error Detected:")
            print("   - Check Firebase security rules")
            print("   - Verify service account permissions")
        elif 'network' in error_str.lower() or 'connection' in error_str.lower():
            print("🔍 Network Error Detected:")
            print("   - Check internet connection")
            print("   - Verify Firebase database URL")
        
        return False

def main():
    """Main function to run all diagnostics"""
    print("🔍 Firebase JWT Debug Tool for Wiz-Aroma V-1.3.3")
    print("=" * 60)
    
    # Check system time
    utc_time = check_system_time()
    
    # Check credentials file
    creds_data = check_credentials_file()
    if not creds_data:
        print("\n❌ Cannot proceed without valid credentials file")
        return False
    
    # Test Firebase initialization
    success = test_firebase_initialization()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests PASSED! Firebase should work correctly.")
        print("✅ The JWT token issue appears to be resolved.")
    else:
        print("🛑 Tests FAILED! Firebase connectivity issues detected.")
        print("❌ Manual intervention may be required.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
