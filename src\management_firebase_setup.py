"""
Firebase Setup for Management Bot
Creates and initializes Firebase collections needed for management functionality.
"""

import datetime
from src.firebase_db import set_data, get_data
from src.config import logger

def initialize_management_collections():
    """Initialize Firebase collections for management bot functionality"""
    
    try:
        # Initialize profit summaries collection
        profit_summaries_structure = {
            "daily": {},
            "weekly": {},
            "monthly": {}
        }
        
        # Initialize personnel earnings collection
        personnel_earnings_structure = {
            "current_month": {},
            "historical": {}
        }
        
        # Initialize management reports collection
        management_reports_structure = {
            "analytics": {},
            "performance": {},
            "earnings": {}
        }
        
        # Initialize management settings collection
        management_settings = {
            "delivery_fee_share_percentage": 50,  # 50% sharing with delivery personnel
            "profit_calculation_method": "revenue_plus_fees",
            "reporting_timezone": "Africa/Addis_Ababa",
            "currency": "birr",
            "last_updated": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Create collections if they don't exist
        existing_profit_summaries = get_data("profit_summaries")
        if not existing_profit_summaries:
            set_data("profit_summaries", profit_summaries_structure)
            logger.info("Created profit_summaries collection")
        
        existing_personnel_earnings = get_data("personnel_earnings")
        if not existing_personnel_earnings:
            set_data("personnel_earnings", personnel_earnings_structure)
            logger.info("Created personnel_earnings collection")
        
        existing_management_reports = get_data("management_reports")
        if not existing_management_reports:
            set_data("management_reports", management_reports_structure)
            logger.info("Created management_reports collection")
        
        existing_management_settings = get_data("management_settings")
        if not existing_management_settings:
            set_data("management_settings", management_settings)
            logger.info("Created management_settings collection")
        
        logger.info("Management Firebase collections initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing management collections: {e}")
        return False

def create_daily_profit_summary(date: str, data: dict):
    """Create or update daily profit summary"""
    try:
        profit_path = f"profit_summaries/daily/{date}"
        set_data(profit_path, data)
        logger.info(f"Created daily profit summary for {date}")
        return True
    except Exception as e:
        logger.error(f"Error creating daily profit summary: {e}")
        return False

def create_personnel_earnings_record(personnel_id: str, month: str, earnings_data: dict):
    """Create or update personnel earnings record"""
    try:
        earnings_path = f"personnel_earnings/historical/{month}/{personnel_id}"
        set_data(earnings_path, earnings_data)
        logger.info(f"Created earnings record for {personnel_id} - {month}")
        return True
    except Exception as e:
        logger.error(f"Error creating personnel earnings record: {e}")
        return False

def save_management_report(report_type: str, report_id: str, report_data: dict):
    """Save a management report"""
    try:
        report_path = f"management_reports/{report_type}/{report_id}"
        report_data['generated_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        set_data(report_path, report_data)
        logger.info(f"Saved {report_type} report: {report_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving management report: {e}")
        return False

def get_management_setting(setting_key: str, default_value=None):
    """Get a management setting value"""
    try:
        settings = get_data("management_settings") or {}
        return settings.get(setting_key, default_value)
    except Exception as e:
        logger.error(f"Error getting management setting {setting_key}: {e}")
        return default_value

def update_management_setting(setting_key: str, value):
    """Update a management setting"""
    try:
        settings_path = f"management_settings/{setting_key}"
        set_data(settings_path, value)
        
        # Update last_updated timestamp
        timestamp_path = "management_settings/last_updated"
        set_data(timestamp_path, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        logger.info(f"Updated management setting {setting_key}")
        return True
    except Exception as e:
        logger.error(f"Error updating management setting {setting_key}: {e}")
        return False

def get_profit_summary(period: str, date_key: str):
    """Get profit summary for a specific period and date"""
    try:
        profit_path = f"profit_summaries/{period}/{date_key}"
        return get_data(profit_path)
    except Exception as e:
        logger.error(f"Error getting profit summary for {period}/{date_key}: {e}")
        return None

def get_personnel_earnings_history(personnel_id: str, month: str = None):
    """Get personnel earnings history"""
    try:
        if month:
            earnings_path = f"personnel_earnings/historical/{month}/{personnel_id}"
            return get_data(earnings_path)
        else:
            # Get all historical earnings for this personnel
            earnings_path = f"personnel_earnings/historical"
            all_earnings = get_data(earnings_path) or {}
            
            personnel_history = {}
            for month_key, month_data in all_earnings.items():
                if personnel_id in month_data:
                    personnel_history[month_key] = month_data[personnel_id]
            
            return personnel_history
    except Exception as e:
        logger.error(f"Error getting personnel earnings history: {e}")
        return None

def cleanup_old_reports(days_to_keep: int = 90):
    """Clean up old management reports"""
    try:
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.strftime('%Y-%m-%d')
        
        # This would need to be implemented based on your specific cleanup needs
        # For now, just log the action
        logger.info(f"Cleanup requested for reports older than {cutoff_str}")
        
        # In a real implementation, you would:
        # 1. Get all reports
        # 2. Check their generated_at timestamps
        # 3. Delete reports older than the cutoff
        
        return True
    except Exception as e:
        logger.error(f"Error cleaning up old reports: {e}")
        return False

def validate_management_data():
    """Validate management data integrity"""
    try:
        validation_results = {
            "profit_summaries": False,
            "personnel_earnings": False,
            "management_reports": False,
            "management_settings": False
        }
        
        # Check if all required collections exist
        for collection in validation_results.keys():
            data = get_data(collection)
            validation_results[collection] = data is not None
        
        all_valid = all(validation_results.values())
        
        if all_valid:
            logger.info("Management data validation passed")
        else:
            logger.warning(f"Management data validation failed: {validation_results}")
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Error validating management data: {e}")
        return None

def export_management_data(export_type: str = "all"):
    """Export management data for backup or analysis"""
    try:
        export_data = {}
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if export_type in ["all", "profit"]:
            export_data["profit_summaries"] = get_data("profit_summaries")
        
        if export_type in ["all", "earnings"]:
            export_data["personnel_earnings"] = get_data("personnel_earnings")
        
        if export_type in ["all", "reports"]:
            export_data["management_reports"] = get_data("management_reports")
        
        if export_type in ["all", "settings"]:
            export_data["management_settings"] = get_data("management_settings")
        
        # Add export metadata
        export_data["export_metadata"] = {
            "export_type": export_type,
            "exported_at": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "export_id": f"mgmt_export_{timestamp}"
        }
        
        logger.info(f"Exported management data: {export_type}")
        return export_data
        
    except Exception as e:
        logger.error(f"Error exporting management data: {e}")
        return None

if __name__ == "__main__":
    # Initialize management collections when run directly
    print("Initializing Management Bot Firebase collections...")
    success = initialize_management_collections()
    
    if success:
        print("✅ Management collections initialized successfully!")
        
        # Validate the setup
        validation = validate_management_data()
        if validation and all(validation.values()):
            print("✅ Data validation passed!")
        else:
            print("⚠️ Data validation issues detected")
            print(f"Validation results: {validation}")
    else:
        print("❌ Failed to initialize management collections")
