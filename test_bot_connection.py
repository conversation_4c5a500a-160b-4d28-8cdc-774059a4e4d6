#!/usr/bin/env python3
"""
Test bot connection without polling
"""

import telebot
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Test bot connection"""
    logger.info("🔍 Testing bot connection...")
    
    token = os.getenv("NOTIFICATION_BOT_TOKEN")
    if not token:
        logger.error("❌ NOTIFICATION_BOT_TOKEN not found")
        return False
    
    logger.info(f"✅ Token loaded (length: {len(token)})")
    
    try:
        # Create bot instance
        bot = telebot.TeleBot(token)
        
        # Test connection
        logger.info("🔍 Testing bot connection...")
        bot_info = bot.get_me()
        logger.info(f"✅ Bot connected: @{bot_info.username} (ID: {bot_info.id})")
        
        # Clear any existing webhooks
        logger.info("🧹 Clearing webhooks...")
        bot.remove_webhook()
        logger.info("✅ Webhooks cleared")
        
        # Test sending a message to authorized user
        authorized_id = 7729984017
        logger.info(f"📤 Sending test message to user {authorized_id}...")
        
        test_message = """
🤖 **MANAGEMENT BOT - CONNECTION TEST**

✅ Bot is online and responsive!
🔧 Callback handlers have been FIXED
📞 Buttons should now respond immediately

**Ready for testing:**
• Send /menu to test the fixed buttons
• Buttons will no longer hang with loading indicators
• Each button click will update the message immediately

**Status**: 🟢 READY FOR LIVE TESTING
"""
        
        bot.send_message(authorized_id, test_message, parse_mode='Markdown')
        logger.info("✅ Test message sent successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing bot: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ Bot connection test PASSED - Ready for live testing!")
    else:
        print("❌ Bot connection test FAILED")
