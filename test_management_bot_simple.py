#!/usr/bin/env python3
"""
Simple test to verify management bot callback functionality
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_callback_structure():
    """Test the callback structure without importing the bot"""
    try:
        logger.info("Testing callback structure...")
        
        # Read the management bot file and check for callback handlers
        with open('src/bots/management_bot_minimal.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for callback handler registration
        if '@management_bot.callback_query_handler' in content:
            logger.info("✅ Callback query handler decorator found")
        else:
            logger.error("❌ No callback query handler decorator found")
            return False
        
        # Check for answer_callback_query calls
        if 'answer_callback_query' in content:
            logger.info("✅ answer_callback_query calls found")
        else:
            logger.error("❌ No answer_callback_query calls found")
            return False
        
        # Check for callback data handling
        callback_patterns = [
            'mgmt_personnel',
            'mgmt_analytics', 
            'mgmt_earnings',
            'mgmt_performance',
            'mgmt_refresh'
        ]
        
        for pattern in callback_patterns:
            if pattern in content:
                logger.info(f"✅ Callback pattern '{pattern}' found")
            else:
                logger.error(f"❌ Callback pattern '{pattern}' not found")
                return False
        
        # Check for callback handler functions
        handler_functions = [
            'handle_personnel_menu',
            'handle_analytics_menu',
            'handle_earnings_menu', 
            'handle_performance_menu',
            'handle_refresh_data',
            'handle_personnel_callbacks',
            'handle_analytics_callbacks',
            'handle_earnings_callbacks',
            'handle_performance_callbacks'
        ]
        
        for func in handler_functions:
            if f'def {func}(' in content:
                logger.info(f"✅ Handler function '{func}' found")
            else:
                logger.error(f"❌ Handler function '{func}' not found")
                return False
        
        logger.info("✅ All callback structure checks passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing callback structure: {e}")
        return False

def test_inline_keyboard_structure():
    """Test inline keyboard structure"""
    try:
        logger.info("Testing inline keyboard structure...")
        
        with open('src/bots/management_bot_minimal.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for inline keyboard creation
        if 'InlineKeyboardMarkup' in content:
            logger.info("✅ InlineKeyboardMarkup found")
        else:
            logger.error("❌ InlineKeyboardMarkup not found")
            return False
        
        if 'InlineKeyboardButton' in content:
            logger.info("✅ InlineKeyboardButton found")
        else:
            logger.error("❌ InlineKeyboardButton not found")
            return False
        
        # Check for button text and callback data
        button_patterns = [
            ('👥 Personnel', 'mgmt_personnel'),
            ('📊 Analytics', 'mgmt_analytics'),
            ('💰 Earnings', 'mgmt_earnings'),
            ('📈 Performance', 'mgmt_performance'),
            ('🔄 Refresh', 'mgmt_refresh')
        ]
        
        for button_text, callback_data in button_patterns:
            if button_text in content and callback_data in content:
                logger.info(f"✅ Button '{button_text}' with callback '{callback_data}' found")
            else:
                logger.error(f"❌ Button '{button_text}' or callback '{callback_data}' not found")
                return False
        
        logger.info("✅ All inline keyboard structure checks passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing inline keyboard structure: {e}")
        return False

def test_authorization_structure():
    """Test authorization structure"""
    try:
        logger.info("Testing authorization structure...")
        
        with open('src/bots/management_bot_minimal.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for authorization function
        if 'def is_authorized(' in content:
            logger.info("✅ is_authorized function found")
        else:
            logger.error("❌ is_authorized function not found")
            return False
        
        # Check for authorized IDs
        if 'MANAGEMENT_BOT_AUTHORIZED_IDS' in content:
            logger.info("✅ MANAGEMENT_BOT_AUTHORIZED_IDS found")
        else:
            logger.error("❌ MANAGEMENT_BOT_AUTHORIZED_IDS not found")
            return False
        
        # Check for authorization checks in callbacks
        if 'if not is_authorized(' in content:
            logger.info("✅ Authorization checks found in callbacks")
        else:
            logger.error("❌ No authorization checks found in callbacks")
            return False
        
        logger.info("✅ All authorization structure checks passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing authorization structure: {e}")
        return False

def main():
    """Run all structure tests"""
    logger.info("🧪 Starting Management Bot Structure Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Callback Structure Test", test_callback_structure),
        ("Inline Keyboard Structure Test", test_inline_keyboard_structure),
        ("Authorization Structure Test", test_authorization_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 MANAGEMENT BOT STRUCTURE TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All structure tests passed!")
        logger.info("✅ Management bot has proper callback structure")
        logger.info("✅ Inline keyboards are properly configured")
        logger.info("✅ Authorization system is in place")
        logger.info("\n💡 The bot should now respond to button clicks!")
        logger.info("💡 Try running: python main.py --management")
        logger.info("💡 Then use /menu command to test buttons")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
