"""
Bot instance initialization for the Wiz Aroma Delivery Bot.
This file initializes the bot instances to avoid circular imports.
"""

import sys
import logging
import telebot
import telebot.apihelper
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get tokens directly from environment
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_BOT_TOKEN = os.getenv("ADMIN_BOT_TOKEN")
FINANCE_BOT_TOKEN = os.getenv("FINANCE_BOT_TOKEN")
MAINTENANCE_BOT_TOKEN = os.getenv("MAINTENANCE_BOT_TOKEN")
NOTIFICATION_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")

# Simple configuration
RETRY_ON_ERROR = True
CONNECT_TIMEOUT = 3
READ_TIMEOUT = 5
TEST_MODE = False

# Simple logger setup
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Configure API request timeouts - increase to avoid connection timeouts
telebot.apihelper.CONNECT_TIMEOUT = 15  # Increased connect timeout
telebot.apihelper.READ_TIMEOUT = 60  # Increased read timeout
telebot.apihelper.RETRY_ON_ERROR = True  # Enable retries
telebot.apihelper.ENABLE_MIDDLEWARE = (
    True  # Enable middleware for better error handling
)

# Set polling timeout to 60 seconds to reduce resource usage
POLLING_TIMEOUT = 60

# Create bot instances with error handling
try:
    if not TEST_MODE:
        # Normal operation with real tokens
        bot = telebot.TeleBot(BOT_TOKEN)
        admin_bot = telebot.TeleBot(ADMIN_BOT_TOKEN)
        finance_bot = telebot.TeleBot(FINANCE_BOT_TOKEN)
        maintenance_bot = telebot.TeleBot(MAINTENANCE_BOT_TOKEN)

        # Import management bot (replaces notification bot, uses same token)
        from src.bots.management_bot_minimal import management_bot

        # Store bot instances for later connection testing (deferred to avoid import hangs)
        bot_instances = [bot, admin_bot, finance_bot, maintenance_bot, management_bot]

        logger.info("Bot instances created successfully (connection testing deferred)")
        logger.info("Management bot has replaced notification bot")
    else:
        # Test mode - create bot instances but don't try to connect
        bot = telebot.TeleBot(BOT_TOKEN)
        admin_bot = telebot.TeleBot(ADMIN_BOT_TOKEN)
        finance_bot = telebot.TeleBot(FINANCE_BOT_TOKEN)
        maintenance_bot = telebot.TeleBot(MAINTENANCE_BOT_TOKEN)

        # Import management bot in test mode too (replaces notification bot)
        from src.bots.management_bot_minimal import management_bot

        logger.info("Running in TEST MODE - bots initialized without API connection")
        logger.info("No Telegram functionality will be available")

except telebot.apihelper.ApiException as e:
    if TEST_MODE:
        logger.warning(f"Telegram API error in test mode: {e}")
        logger.info("Continuing in test mode with limited functionality")
    else:
        logger.error(f"Telegram API error: {e}")
        logger.error("Please check your bot tokens in the .env file")
        # Log detailed error information
        logger.error(f"API Exception details: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        sys.exit(1)
except Exception as e:
    if TEST_MODE:
        logger.warning(f"Error initializing bots in test mode: {e}")
        logger.info("Continuing in test mode with limited functionality")
    else:
        logger.error(f"Error initializing bots: {e}")
        sys.exit(1)

# Set longer session lifetime to reduce reconnections
telebot.apihelper.SESSION_TIME_TO_LIVE = 10 * 60  # 10 minutes session time to live
telebot.apihelper.MAX_RETRIES = 5  # Increase max retries


# Add error handling functions to bots
def _handle_api_exception(exception):
    """Handle API exceptions with custom logic"""
    logger.error(f"API exception: {exception}")
    # You can add custom handling based on exception type
    # For example, handle rate limiting, connection timeouts, etc.
    return True  # Return True to indicate exception was handled


# Apply error handlers to bots
try:
    # Use the same management bot that's already imported above
    bot_instances = [bot, admin_bot, finance_bot, maintenance_bot, management_bot]  # Removed notification_bot
except NameError:
    # Fallback if management bot is not available
    bot_instances = [bot, admin_bot, finance_bot, maintenance_bot]  # Removed notification_bot

for bot_instance in bot_instances:
    bot_instance.exception_handler = type(
        "ExceptionHandler", (), {"handle": _handle_api_exception}
    )


def register_all_handlers(bot_instances):
    """Register all handlers on the bot instances"""
    from src.utils.handler_registration import register_handlers_for_bots

    # Register handlers
    register_handlers_for_bots(bot_instances)

    # Log to the console
    logger.info("All handlers registered successfully")
