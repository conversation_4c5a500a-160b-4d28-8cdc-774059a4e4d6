"""
Bot instance initialization for the Wiz Aroma Delivery Bot.
This file initializes the bot instances to avoid circular imports.
"""

import sys
import logging
import telebot
import telebot.apihelper
from src.config import (
    BOT_TOKEN,
    ADMIN_BOT_TOKEN,
    FINANCE_BOT_TOKEN,
    MAINTENANCE_BOT_TOKEN,
    NOTIFICATION_BOT_TOKEN,
    RETRY_ON_ERROR,
    CONNECT_TIMEOUT,
    READ_TIMEOUT,
    TEST_MODE,
    logger,
)

# Configure API request timeouts - increase to avoid connection timeouts
telebot.apihelper.CONNECT_TIMEOUT = 15  # Increased connect timeout
telebot.apihelper.READ_TIMEOUT = 60  # Increased read timeout
telebot.apihelper.RETRY_ON_ERROR = True  # Enable retries
telebot.apihelper.ENABLE_MIDDLEWARE = (
    True  # Enable middleware for better error handling
)

# Set polling timeout to 60 seconds to reduce resource usage
POLLING_TIMEOUT = 60

# Create bot instances with error handling
try:
    if not TEST_MODE:
        # Normal operation with real tokens
        bot = telebot.TeleBot(BOT_TOKEN)
        admin_bot = telebot.TeleBot(ADMIN_BOT_TOKEN)
        finance_bot = telebot.TeleBot(FINANCE_BOT_TOKEN)
        maintenance_bot = telebot.TeleBot(MAINTENANCE_BOT_TOKEN)
        notification_bot = telebot.TeleBot(NOTIFICATION_BOT_TOKEN)

        # Test connections to ensure tokens are valid
        bot_info = bot.get_me()
        admin_bot_info = admin_bot.get_me()
        finance_bot_info = finance_bot.get_me()
        maintenance_bot_info = maintenance_bot.get_me()
        notification_bot_info = notification_bot.get_me()

        logger.info(f"User bot connected successfully: @{bot_info.username}")
        logger.info(f"Admin bot connected successfully: @{admin_bot_info.username}")
        logger.info(f"Finance bot connected successfully: @{finance_bot_info.username}")
        logger.info(
            f"Maintenance bot connected successfully: @{maintenance_bot_info.username}"
        )
        logger.info(
            f"Notification bot connected successfully: @{notification_bot_info.username}"
        )
    else:
        # Test mode - create bot instances but don't try to connect
        bot = telebot.TeleBot(BOT_TOKEN)
        admin_bot = telebot.TeleBot(ADMIN_BOT_TOKEN)
        finance_bot = telebot.TeleBot(FINANCE_BOT_TOKEN)
        maintenance_bot = telebot.TeleBot(MAINTENANCE_BOT_TOKEN)
        notification_bot = telebot.TeleBot(NOTIFICATION_BOT_TOKEN)

        logger.info("Running in TEST MODE - bots initialized without API connection")
        logger.info("No Telegram functionality will be available")

except telebot.apihelper.ApiException as e:
    if TEST_MODE:
        logger.warning(f"Telegram API error in test mode: {e}")
        logger.info("Continuing in test mode with limited functionality")
    else:
        logger.error(f"Telegram API error: {e}")
        logger.error("Please check your bot tokens in the .env file")
        # Log detailed error information
        logger.error(f"API Exception details: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        sys.exit(1)
except Exception as e:
    if TEST_MODE:
        logger.warning(f"Error initializing bots in test mode: {e}")
        logger.info("Continuing in test mode with limited functionality")
    else:
        logger.error(f"Error initializing bots: {e}")
        sys.exit(1)

# Set longer session lifetime to reduce reconnections
telebot.apihelper.SESSION_TIME_TO_LIVE = 10 * 60  # 10 minutes session time to live
telebot.apihelper.MAX_RETRIES = 5  # Increase max retries


# Add error handling functions to bots
def _handle_api_exception(exception):
    """Handle API exceptions with custom logic"""
    logger.error(f"API exception: {exception}")
    # You can add custom handling based on exception type
    # For example, handle rate limiting, connection timeouts, etc.
    return True  # Return True to indicate exception was handled


# Apply error handlers to bots
for bot_instance in [bot, admin_bot, finance_bot, maintenance_bot, notification_bot]:
    bot_instance.exception_handler = type(
        "ExceptionHandler", (), {"handle": _handle_api_exception}
    )


def register_all_handlers(bot_instances):
    """Register all handlers on the bot instances"""
    from src.utils.handler_registration import register_handlers_for_bots

    # Register handlers
    register_handlers_for_bots(bot_instances)

    # Log to the console
    logger.info("All handlers registered successfully")
