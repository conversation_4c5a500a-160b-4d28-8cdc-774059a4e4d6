"""
Management Bot for Wiz Aroma Delivery System
Comprehensive management system for delivery personnel, analytics, and reporting.
Replaces the notification bot with enhanced management capabilities.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging
import uuid

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get management bot token (same as notification bot for continuity)
MANAGEMENT_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")

# Management bot authorized users
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017]  # Same as order tracking bot

# Simple logger setup
import logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
# Lazy imports to avoid circular dependencies and initialization issues
# These will be imported only when needed
def get_firebase_functions():
    """Lazy import Firebase functions to avoid initialization issues"""
    try:
        from src.firebase_db import get_data, set_data, delete_data
        return get_data, set_data, delete_data
    except ImportError as e:
        logger.warning(f"Firebase functions not available: {e}")
        return None, None, None

def get_delivery_personnel_functions():
    """Lazy import delivery personnel functions"""
    try:
        from src.data_models import DeliveryPersonnel
        from src.utils.delivery_personnel_utils import (
            create_delivery_personnel,
            get_delivery_personnel_by_telegram_id,
            get_delivery_personnel_by_id
        )
        return DeliveryPersonnel, create_delivery_personnel, get_delivery_personnel_by_telegram_id, get_delivery_personnel_by_id
    except ImportError as e:
        logger.warning(f"Delivery personnel functions not available: {e}")
        return None, None, None, None

# Initialize management bot
management_bot = telebot.TeleBot(MANAGEMENT_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to use management bot"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

def send_unauthorized_message(user_id: int):
    """Send unauthorized access message"""
    try:
        management_bot.send_message(
            user_id,
            "🚫 *Access Denied*\n\n"
            "You are not authorized to use this management bot.\n"
            "Please contact the system administrator for access.",
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Error sending unauthorized message: {e}")

# ============================================================================
# COMMAND HANDLERS
# ============================================================================

@management_bot.message_handler(commands=['start', 'help'])
def handle_start(message):
    """Handle start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        send_unauthorized_message(user_id)
        return
    
    welcome_text = (
        "🏢 *Wiz Aroma Management Bot*\n\n"
        "Welcome to the comprehensive management system!\n\n"
        "*Available Features:*\n"
        "👥 Delivery Personnel Management\n"
        "📊 Analytics & Reporting\n"
        "💰 Earnings Calculation\n"
        "📈 Performance Tracking\n\n"
        "Use /menu to access all management features."
    )
    
    try:
        management_bot.send_message(
            user_id,
            welcome_text,
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Error sending start message: {e}")

@management_bot.message_handler(commands=['menu'])
def handle_menu(message):
    """Show main management menu"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        send_unauthorized_message(user_id)
        return
    
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👥 Personnel Management", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics & Reports", callback_data="mgmt_analytics")
    )
    markup.add(
        types.InlineKeyboardButton("💰 Earnings & Profits", callback_data="mgmt_earnings"),
        types.InlineKeyboardButton("📈 Performance Stats", callback_data="mgmt_performance")
    )
    markup.add(
        types.InlineKeyboardButton("🔄 Refresh Data", callback_data="mgmt_refresh")
    )
    
    try:
        management_bot.send_message(
            user_id,
            "🏢 *Management Dashboard*\n\nSelect a management area:",
            reply_markup=markup,
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Error sending menu: {e}")

# ============================================================================
# CALLBACK HANDLERS
# ============================================================================

@management_bot.callback_query_handler(func=lambda call: call.data.startswith('mgmt_'))
def handle_management_callbacks(call):
    """Handle management bot callbacks"""
    user_id = call.from_user.id

    if not is_authorized(user_id):
        management_bot.answer_callback_query(call.id, "❌ Unauthorized access")
        send_unauthorized_message(user_id)
        return

    try:
        # Answer the callback query first to remove loading state
        management_bot.answer_callback_query(call.id)
        if call.data == "mgmt_personnel":
            show_personnel_management(call)
        elif call.data == "mgmt_analytics":
            show_analytics_menu(call)
        elif call.data == "mgmt_earnings":
            show_earnings_menu(call)
        elif call.data == "mgmt_performance":
            show_performance_menu(call)
        elif call.data == "mgmt_refresh":
            refresh_management_data(call)
        elif call.data.startswith("personnel_"):
            handle_personnel_callbacks(call)
        elif call.data.startswith("analytics_"):
            handle_analytics_callbacks(call)
        elif call.data.startswith("earnings_"):
            handle_earnings_callbacks(call)
        elif call.data.startswith("performance_"):
            handle_performance_callbacks(call)
        else:
            # Unknown callback
            management_bot.answer_callback_query(call.id, "Unknown action")
            
    except Exception as e:
        logger.error(f"Error handling callback {call.data}: {e}")
        management_bot.answer_callback_query(call.id, "Error processing request")

@management_bot.callback_query_handler(func=lambda call: True)
def handle_all_callbacks(call):
    """Handle all other callback queries not caught by specific handlers"""
    user_id = call.from_user.id

    if not is_authorized(user_id):
        management_bot.answer_callback_query(call.id, "❌ Unauthorized access")
        return

    try:
        # Answer the callback query first
        management_bot.answer_callback_query(call.id)

        # Handle various callback patterns
        if call.data.startswith('personnel_'):
            handle_personnel_callbacks(call)
        elif call.data.startswith('analytics_'):
            handle_analytics_callbacks(call)
        elif call.data.startswith('earnings_'):
            handle_earnings_callbacks(call)
        elif call.data.startswith('performance_'):
            handle_performance_callbacks(call)
        elif call.data == "back_to_menu":
            show_main_menu(call)
        else:
            # Unknown callback
            logger.warning(f"Unknown callback data: {call.data}")
            management_bot.edit_message_text(
                f"⚠️ **Unknown Action**\n\nCallback '{call.data}' is not implemented.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )

    except Exception as e:
        logger.error(f"Error handling general callback {call.data}: {e}")
        try:
            management_bot.edit_message_text(
                "❌ **Error**\n\nAn error occurred processing your request.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        except:
            management_bot.send_message(
                call.message.chat.id,
                "❌ Error occurred. Use /menu to access management functions."
            )

# ============================================================================
# PERSONNEL MANAGEMENT
# ============================================================================

def show_personnel_management(call):
    """Show personnel management menu"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("➕ Add Personnel", callback_data="personnel_add"),
        types.InlineKeyboardButton("👥 View All", callback_data="personnel_view_all")
    )
    markup.add(
        types.InlineKeyboardButton("🗑️ Remove Personnel", callback_data="personnel_remove"),
        types.InlineKeyboardButton("✏️ Edit Personnel", callback_data="personnel_edit")
    )
    markup.add(
        types.InlineKeyboardButton("🔍 Search by Telegram ID", callback_data="personnel_search"),
        types.InlineKeyboardButton("📊 Personnel Stats", callback_data="personnel_stats")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back to Menu", callback_data="mgmt_back_to_menu")
    )
    
    try:
        management_bot.edit_message_text(
            "👥 *Personnel Management*\n\nSelect an action:",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)
    except Exception as e:
        logger.error(f"Error showing personnel management: {e}")
        management_bot.answer_callback_query(call.id, "Error loading personnel management")

def handle_personnel_callbacks(call):
    """Handle personnel management callbacks"""
    if call.data == "personnel_add":
        start_add_personnel_flow(call)
    elif call.data == "personnel_view_all":
        show_all_personnel(call)
    elif call.data == "personnel_remove":
        start_remove_personnel_flow(call)
    elif call.data == "personnel_edit":
        start_edit_personnel_flow(call)
    elif call.data == "personnel_search":
        start_search_personnel_flow(call)
    elif call.data == "personnel_stats":
        show_personnel_statistics(call)
    elif call.data == "mgmt_back_to_menu":
        # Go back to main menu
        handle_menu_from_callback(call)

def start_add_personnel_flow(call):
    """Start the flow to add new personnel"""
    try:
        management_bot.edit_message_text(
            "➕ *Add New Personnel*\n\n"
            "Please provide the following information:\n"
            "1. Full Name\n"
            "2. Phone Number\n"
            "3. Telegram ID\n"
            "4. Service Areas (comma-separated)\n\n"
            "Format: `Name | Phone | TelegramID | Areas`\n"
            "Example: `John Doe | +251912345678 | 123456789 | 1,2,3`\n\n"
            "Send the information in the above format:",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )
        
        # Set user state for next message
        # Note: In a production system, you'd want to use a proper state management system
        management_bot.answer_callback_query(call.id)
        
    except Exception as e:
        logger.error(f"Error starting add personnel flow: {e}")
        management_bot.answer_callback_query(call.id, "Error starting add personnel flow")

def show_all_personnel(call):
    """Show all delivery personnel"""
    try:
        # Get personnel data from Firebase
        personnel_data = get_data("delivery_personnel") or {}
        
        if not personnel_data:
            management_bot.edit_message_text(
                "👥 *All Personnel*\n\n"
                "No delivery personnel found in the system.\n"
                "Use 'Add Personnel' to add new delivery staff.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return
        
        # Format personnel list
        personnel_list = "👥 *All Delivery Personnel*\n\n"
        
        for personnel_id, data in personnel_data.items():
            name = data.get('name', 'Unknown')
            phone = data.get('phone_number', 'N/A')
            telegram_id = data.get('telegram_id', 'N/A')
            status = data.get('status', 'offline')
            total_deliveries = data.get('total_deliveries', 0)
            
            status_emoji = "🟢" if status == "available" else "🔴" if status == "offline" else "🟡"
            
            personnel_list += (
                f"{status_emoji} *{name}*\n"
                f"📱 Phone: {phone}\n"
                f"💬 Telegram: {telegram_id}\n"
                f"📦 Deliveries: {total_deliveries}\n"
                f"📍 Status: {status.title()}\n\n"
            )
        
        # Add back button
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_personnel"))
        
        management_bot.edit_message_text(
            personnel_list,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)
        
    except Exception as e:
        logger.error(f"Error showing all personnel: {e}")
        management_bot.answer_callback_query(call.id, "Error loading personnel data")

def handle_menu_from_callback(call):
    """Handle returning to main menu from callback"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👥 Personnel Management", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics & Reports", callback_data="mgmt_analytics")
    )
    markup.add(
        types.InlineKeyboardButton("💰 Earnings & Profits", callback_data="mgmt_earnings"),
        types.InlineKeyboardButton("📈 Performance Stats", callback_data="mgmt_performance")
    )
    markup.add(
        types.InlineKeyboardButton("🔄 Refresh Data", callback_data="mgmt_refresh")
    )
    
    try:
        management_bot.edit_message_text(
            "🏢 *Management Dashboard*\n\nSelect a management area:",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)
    except Exception as e:
        logger.error(f"Error returning to menu: {e}")
        management_bot.answer_callback_query(call.id, "Error loading menu")

# ============================================================================
# ANALYTICS AND REPORTING
# ============================================================================

def show_analytics_menu(call):
    """Show analytics and reporting menu"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("📅 Daily Reports", callback_data="analytics_daily"),
        types.InlineKeyboardButton("📊 Weekly Reports", callback_data="analytics_weekly")
    )
    markup.add(
        types.InlineKeyboardButton("📈 Monthly Reports", callback_data="analytics_monthly"),
        types.InlineKeyboardButton("📋 Transaction Count", callback_data="analytics_transactions")
    )
    markup.add(
        types.InlineKeyboardButton("🚚 Delivery Reports", callback_data="analytics_delivery"),
        types.InlineKeyboardButton("💰 Profit Summary", callback_data="analytics_profit")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back to Menu", callback_data="mgmt_back_to_menu")
    )

    try:
        management_bot.edit_message_text(
            "📊 *Analytics & Reports*\n\nSelect a report type:",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)
    except Exception as e:
        logger.error(f"Error showing analytics menu: {e}")
        management_bot.answer_callback_query(call.id, "Error loading analytics menu")

def show_earnings_menu(call):
    """Show earnings and profit menu"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("💰 Total Profits", callback_data="earnings_total"),
        types.InlineKeyboardButton("👥 Personnel Earnings", callback_data="earnings_personnel")
    )
    markup.add(
        types.InlineKeyboardButton("📅 Daily Earnings", callback_data="earnings_daily"),
        types.InlineKeyboardButton("📊 Weekly Earnings", callback_data="earnings_weekly")
    )
    markup.add(
        types.InlineKeyboardButton("📈 Monthly Earnings", callback_data="earnings_monthly"),
        types.InlineKeyboardButton("🚚 Delivery Fee Share", callback_data="earnings_delivery_share")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back to Menu", callback_data="mgmt_back_to_menu")
    )

    try:
        management_bot.edit_message_text(
            "💰 *Earnings & Profits*\n\nSelect an earnings report:",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)
    except Exception as e:
        logger.error(f"Error showing earnings menu: {e}")
        management_bot.answer_callback_query(call.id, "Error loading earnings menu")

def show_performance_menu(call):
    """Show performance tracking menu"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("🏆 Top Performers", callback_data="performance_top"),
        types.InlineKeyboardButton("📊 Performance Stats", callback_data="performance_stats")
    )
    markup.add(
        types.InlineKeyboardButton("⏱️ Delivery Times", callback_data="performance_times"),
        types.InlineKeyboardButton("⭐ Ratings", callback_data="performance_ratings")
    )
    markup.add(
        types.InlineKeyboardButton("📈 Trends", callback_data="performance_trends"),
        types.InlineKeyboardButton("🎯 Goals & Targets", callback_data="performance_goals")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back to Menu", callback_data="mgmt_back_to_menu")
    )

    try:
        management_bot.edit_message_text(
            "📈 *Performance Tracking*\n\nSelect a performance metric:",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)
    except Exception as e:
        logger.error(f"Error showing performance menu: {e}")
        management_bot.answer_callback_query(call.id, "Error loading performance menu")

def refresh_management_data(call):
    """Refresh all management data from Firebase"""
    try:
        # Show loading message
        management_bot.edit_message_text(
            "🔄 *Refreshing Data...*\n\nPlease wait while we update all management data.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

        # Refresh data from Firebase
        personnel_data = get_data("delivery_personnel") or {}
        completed_orders = get_data("completed_orders") or {}
        assignments = get_data("delivery_personnel_assignments") or {}

        # Calculate refresh statistics
        personnel_count = len(personnel_data)
        completed_count = len(completed_orders)
        assignments_count = len(assignments)

        # Show refresh results
        refresh_text = (
            "✅ *Data Refresh Complete*\n\n"
            f"👥 Personnel Records: {personnel_count}\n"
            f"📦 Completed Orders: {completed_count}\n"
            f"🚚 Active Assignments: {assignments_count}\n\n"
            f"🕐 Last Updated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Menu", callback_data="mgmt_back_to_menu"))

        management_bot.edit_message_text(
            refresh_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id, "Data refreshed successfully!")

    except Exception as e:
        logger.error(f"Error refreshing data: {e}")
        management_bot.answer_callback_query(call.id, "Error refreshing data")

def handle_analytics_callbacks(call):
    """Handle analytics-related callbacks"""
    if call.data == "analytics_daily":
        show_daily_analytics(call)
    elif call.data == "analytics_weekly":
        show_weekly_analytics(call)
    elif call.data == "analytics_monthly":
        show_monthly_analytics(call)
    elif call.data == "analytics_transactions":
        show_transaction_count(call)
    elif call.data == "analytics_delivery":
        show_delivery_reports(call)
    elif call.data == "analytics_profit":
        show_profit_summary(call)

def handle_earnings_callbacks(call):
    """Handle earnings-related callbacks"""
    if call.data == "earnings_total":
        show_total_profits(call)
    elif call.data == "earnings_personnel":
        show_personnel_earnings(call)
    elif call.data == "earnings_daily":
        show_daily_earnings(call)
    elif call.data == "earnings_weekly":
        show_weekly_earnings(call)
    elif call.data == "earnings_monthly":
        show_monthly_earnings(call)
    elif call.data == "earnings_delivery_share":
        show_delivery_fee_sharing(call)

def handle_performance_callbacks(call):
    """Handle performance-related callbacks"""
    if call.data == "performance_top":
        show_top_performers(call)
    elif call.data == "performance_stats":
        show_performance_statistics(call)
    elif call.data == "performance_times":
        show_delivery_times(call)
    elif call.data == "performance_ratings":
        show_performance_ratings(call)
    elif call.data == "performance_trends":
        show_performance_trends(call)
    elif call.data == "performance_goals":
        show_performance_goals(call)

# ============================================================================
# ANALYTICS CALCULATION FUNCTIONS
# ============================================================================

def calculate_daily_profits(target_date: datetime.date = None) -> Dict[str, Any]:
    """Calculate daily profit summary"""
    if target_date is None:
        target_date = datetime.date.today()

    try:
        # Get completed orders for the target date
        completed_orders = get_data("completed_orders") or {}

        daily_orders = []
        total_revenue = 0
        total_delivery_fees = 0
        total_orders = 0

        for order_number, order_data in completed_orders.items():
            completed_at = order_data.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.datetime.strptime(completed_at, '%Y-%m-%d %H:%M:%S').date()
                    if order_date == target_date:
                        daily_orders.append(order_data)
                        total_orders += 1
                        total_revenue += float(order_data.get('subtotal', 0))
                        total_delivery_fees += float(order_data.get('delivery_fee', 0))
                except (ValueError, TypeError):
                    continue

        # Calculate profit (assuming 100% of subtotal + delivery fees as profit for now)
        # In a real system, you'd subtract costs
        total_profit = total_revenue + total_delivery_fees

        return {
            'date': target_date.strftime('%Y-%m-%d'),
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'total_delivery_fees': total_delivery_fees,
            'total_profit': total_profit,
            'orders': daily_orders
        }
    except Exception as e:
        logger.error(f"Error calculating daily profits: {e}")
        return {}

def calculate_personnel_earnings(personnel_id: str = None, start_date: datetime.date = None, end_date: datetime.date = None) -> Dict[str, Any]:
    """Calculate delivery personnel earnings (50% of delivery fees)"""
    if end_date is None:
        end_date = datetime.date.today()
    if start_date is None:
        start_date = end_date - datetime.timedelta(days=30)  # Default to last 30 days

    try:
        # Get completed assignments
        assignments = get_data("delivery_personnel_assignments") or {}
        completed_orders = get_data("completed_orders") or {}

        personnel_earnings = {}

        for assignment_id, assignment_data in assignments.items():
            if assignment_data.get('status') == 'delivered':
                assignment_personnel_id = assignment_data.get('personnel_id')
                order_number = assignment_data.get('order_number')

                # Filter by personnel if specified
                if personnel_id and assignment_personnel_id != personnel_id:
                    continue

                # Get order completion date
                order_data = completed_orders.get(order_number, {})
                completed_at = order_data.get('completed_at', '')

                if completed_at:
                    try:
                        completion_date = datetime.datetime.strptime(completed_at, '%Y-%m-%d %H:%M:%S').date()
                        if start_date <= completion_date <= end_date:
                            delivery_fee = float(assignment_data.get('delivery_fee', 0))
                            personnel_share = delivery_fee * 0.5  # 50% sharing

                            if assignment_personnel_id not in personnel_earnings:
                                personnel_earnings[assignment_personnel_id] = {
                                    'total_earnings': 0,
                                    'total_deliveries': 0,
                                    'total_delivery_fees': 0,
                                    'orders': []
                                }

                            personnel_earnings[assignment_personnel_id]['total_earnings'] += personnel_share
                            personnel_earnings[assignment_personnel_id]['total_deliveries'] += 1
                            personnel_earnings[assignment_personnel_id]['total_delivery_fees'] += delivery_fee
                            personnel_earnings[assignment_personnel_id]['orders'].append({
                                'order_number': order_number,
                                'delivery_fee': delivery_fee,
                                'personnel_share': personnel_share,
                                'completed_at': completed_at
                            })
                    except (ValueError, TypeError):
                        continue

        return personnel_earnings
    except Exception as e:
        logger.error(f"Error calculating personnel earnings: {e}")
        return {}

def show_daily_analytics(call):
    """Show daily analytics report"""
    try:
        daily_data = calculate_daily_profits()

        if not daily_data:
            management_bot.edit_message_text(
                "📅 *Daily Analytics*\n\nNo data available for today.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return

        report_text = (
            f"📅 *Daily Analytics - {daily_data['date']}*\n\n"
            f"📦 Total Orders: {daily_data['total_orders']}\n"
            f"💰 Total Revenue: {daily_data['total_revenue']:.2f} birr\n"
            f"🚚 Delivery Fees: {daily_data['total_delivery_fees']:.2f} birr\n"
            f"📈 Total Profit: {daily_data['total_profit']:.2f} birr\n\n"
            f"📊 Average Order Value: {(daily_data['total_revenue'] / max(daily_data['total_orders'], 1)):.2f} birr"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Analytics", callback_data="mgmt_analytics"))

        management_bot.edit_message_text(
            report_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing daily analytics: {e}")
        management_bot.answer_callback_query(call.id, "Error loading daily analytics")

def show_personnel_earnings(call):
    """Show personnel earnings report"""
    try:
        earnings_data = calculate_personnel_earnings()
        personnel_data = get_data("delivery_personnel") or {}

        if not earnings_data:
            management_bot.edit_message_text(
                "💰 *Personnel Earnings*\n\nNo earnings data available for the last 30 days.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return

        report_text = "💰 *Personnel Earnings (Last 30 Days)*\n\n"

        for personnel_id, earnings in earnings_data.items():
            personnel_info = personnel_data.get(personnel_id, {})
            name = personnel_info.get('name', 'Unknown')

            report_text += (
                f"👤 *{name}*\n"
                f"💰 Earnings: {earnings['total_earnings']:.2f} birr\n"
                f"📦 Deliveries: {earnings['total_deliveries']}\n"
                f"🚚 Total Fees: {earnings['total_delivery_fees']:.2f} birr\n\n"
            )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Earnings", callback_data="mgmt_earnings"))

        management_bot.edit_message_text(
            report_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing personnel earnings: {e}")
        management_bot.answer_callback_query(call.id, "Error loading personnel earnings")

# ============================================================================
# PERSONNEL MANAGEMENT FUNCTIONS
# ============================================================================

def start_remove_personnel_flow(call):
    """Start remove personnel flow - placeholder"""
    management_bot.answer_callback_query(call.id, "Remove personnel - Coming soon!")

def start_edit_personnel_flow(call):
    """Start edit personnel flow - placeholder"""
    management_bot.answer_callback_query(call.id, "Edit personnel - Coming soon!")

def start_search_personnel_flow(call):
    """Start search personnel flow - placeholder"""
    management_bot.answer_callback_query(call.id, "Search personnel - Coming soon!")

def show_personnel_statistics(call):
    """Show personnel statistics"""
    try:
        personnel_data = get_data("delivery_personnel") or {}
        availability_data = get_data("delivery_personnel_availability") or {}

        if not personnel_data:
            management_bot.edit_message_text(
                "📊 *Personnel Statistics*\n\nNo personnel data available.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return

        total_personnel = len(personnel_data)
        available_count = sum(1 for status in availability_data.values() if status == "available")
        offline_count = sum(1 for status in availability_data.values() if status == "offline")
        busy_count = total_personnel - available_count - offline_count

        total_deliveries = sum(p.get('total_deliveries', 0) for p in personnel_data.values())
        avg_deliveries = total_deliveries / max(total_personnel, 1)

        stats_text = (
            "📊 *Personnel Statistics*\n\n"
            f"👥 Total Personnel: {total_personnel}\n"
            f"🟢 Available: {available_count}\n"
            f"🟡 Busy: {busy_count}\n"
            f"🔴 Offline: {offline_count}\n\n"
            f"📦 Total Deliveries: {total_deliveries}\n"
            f"📈 Average per Person: {avg_deliveries:.1f}\n"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel"))

        management_bot.edit_message_text(
            stats_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing personnel statistics: {e}")
        management_bot.answer_callback_query(call.id, "Error loading personnel statistics")

# ============================================================================
# ADDITIONAL ANALYTICS FUNCTIONS
# ============================================================================

def show_weekly_analytics(call):
    """Show weekly analytics - placeholder for now"""
    management_bot.answer_callback_query(call.id, "Weekly analytics - Coming soon!")

def show_monthly_analytics(call):
    """Show monthly analytics - placeholder for now"""
    management_bot.answer_callback_query(call.id, "Monthly analytics - Coming soon!")

def show_transaction_count(call):
    """Show transaction count analytics"""
    try:
        completed_orders = get_data("completed_orders") or {}
        today = datetime.date.today()

        # Count transactions by period
        daily_count = 0
        weekly_count = 0
        monthly_count = 0

        for order_data in completed_orders.values():
            completed_at = order_data.get('completed_at', '')
            if completed_at:
                try:
                    completion_date = datetime.datetime.strptime(completed_at, '%Y-%m-%d %H:%M:%S').date()

                    # Daily count
                    if completion_date == today:
                        daily_count += 1

                    # Weekly count (last 7 days)
                    if (today - completion_date).days <= 7:
                        weekly_count += 1

                    # Monthly count (last 30 days)
                    if (today - completion_date).days <= 30:
                        monthly_count += 1

                except (ValueError, TypeError):
                    continue

        transaction_text = (
            "📋 *Transaction Count*\n\n"
            f"📅 Today: {daily_count} orders\n"
            f"📊 Last 7 Days: {weekly_count} orders\n"
            f"📈 Last 30 Days: {monthly_count} orders\n\n"
            f"📊 Daily Average (30d): {monthly_count / 30:.1f} orders\n"
            f"📈 Weekly Average (30d): {monthly_count / 4.3:.1f} orders"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Analytics", callback_data="mgmt_analytics"))

        management_bot.edit_message_text(
            transaction_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing transaction count: {e}")
        management_bot.answer_callback_query(call.id, "Error loading transaction count")

def show_delivery_reports(call):
    """Show delivery assignment reports"""
    try:
        assignments = get_data("delivery_personnel_assignments") or {}
        personnel_data = get_data("delivery_personnel") or {}

        if not assignments:
            management_bot.edit_message_text(
                "🚚 *Delivery Reports*\n\nNo delivery assignments found.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return

        # Group assignments by personnel
        personnel_assignments = {}
        for assignment_data in assignments.values():
            personnel_id = assignment_data.get('personnel_id')
            if personnel_id not in personnel_assignments:
                personnel_assignments[personnel_id] = []
            personnel_assignments[personnel_id].append(assignment_data)

        report_text = "🚚 *Delivery Assignment Reports*\n\n"

        for personnel_id, assignments_list in personnel_assignments.items():
            personnel_info = personnel_data.get(personnel_id, {})
            name = personnel_info.get('name', 'Unknown')

            completed = sum(1 for a in assignments_list if a.get('status') == 'delivered')
            total = len(assignments_list)

            report_text += (
                f"👤 *{name}*\n"
                f"📦 Completed: {completed}/{total}\n"
                f"📊 Success Rate: {(completed/max(total,1)*100):.1f}%\n\n"
            )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Analytics", callback_data="mgmt_analytics"))

        management_bot.edit_message_text(
            report_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing delivery reports: {e}")
        management_bot.answer_callback_query(call.id, "Error loading delivery reports")

def show_profit_summary(call):
    """Show profit summary"""
    try:
        # Calculate profits for different periods
        today_profits = calculate_daily_profits()

        # Calculate weekly profits (last 7 days)
        weekly_total = 0
        weekly_orders = 0
        for i in range(7):
            date = datetime.date.today() - datetime.timedelta(days=i)
            daily_data = calculate_daily_profits(date)
            weekly_total += daily_data.get('total_profit', 0)
            weekly_orders += daily_data.get('total_orders', 0)

        profit_text = (
            "💰 *Profit Summary*\n\n"
            f"📅 Today's Profit: {today_profits.get('total_profit', 0):.2f} birr\n"
            f"📦 Today's Orders: {today_profits.get('total_orders', 0)}\n\n"
            f"📊 Weekly Profit (7d): {weekly_total:.2f} birr\n"
            f"📈 Weekly Orders (7d): {weekly_orders}\n\n"
            f"📊 Daily Average: {weekly_total/7:.2f} birr\n"
            f"📈 Order Average: {weekly_orders/7:.1f} orders/day"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Analytics", callback_data="mgmt_analytics"))

        management_bot.edit_message_text(
            profit_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing profit summary: {e}")
        management_bot.answer_callback_query(call.id, "Error loading profit summary")

# ============================================================================
# EARNINGS FUNCTIONS
# ============================================================================

def show_total_profits(call):
    """Show total profits across all time"""
    try:
        completed_orders = get_data("completed_orders") or {}

        total_revenue = 0
        total_delivery_fees = 0
        total_orders = len(completed_orders)

        for order_data in completed_orders.values():
            total_revenue += float(order_data.get('subtotal', 0))
            total_delivery_fees += float(order_data.get('delivery_fee', 0))

        total_profit = total_revenue + total_delivery_fees

        profit_text = (
            "💰 *Total Profits (All Time)*\n\n"
            f"📦 Total Orders: {total_orders}\n"
            f"💰 Total Revenue: {total_revenue:.2f} birr\n"
            f"🚚 Total Delivery Fees: {total_delivery_fees:.2f} birr\n"
            f"📈 Total Profit: {total_profit:.2f} birr\n\n"
            f"📊 Average Order Value: {(total_revenue / max(total_orders, 1)):.2f} birr\n"
            f"🚚 Average Delivery Fee: {(total_delivery_fees / max(total_orders, 1)):.2f} birr"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Earnings", callback_data="mgmt_earnings"))

        management_bot.edit_message_text(
            profit_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing total profits: {e}")
        management_bot.answer_callback_query(call.id, "Error loading total profits")

def show_daily_earnings(call):
    """Show daily earnings breakdown"""
    try:
        daily_data = calculate_daily_profits()
        earnings_data = calculate_personnel_earnings(start_date=datetime.date.today(), end_date=datetime.date.today())

        total_personnel_earnings = sum(e['total_earnings'] for e in earnings_data.values())
        company_earnings = daily_data.get('total_delivery_fees', 0) - total_personnel_earnings

        earnings_text = (
            f"💰 *Daily Earnings - {daily_data.get('date', 'Today')}*\n\n"
            f"📈 Total Profit: {daily_data.get('total_profit', 0):.2f} birr\n"
            f"🚚 Delivery Fees: {daily_data.get('total_delivery_fees', 0):.2f} birr\n\n"
            f"👥 Personnel Share (50%): {total_personnel_earnings:.2f} birr\n"
            f"🏢 Company Share (50%): {company_earnings:.2f} birr\n\n"
            f"📦 Orders Processed: {daily_data.get('total_orders', 0)}"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Earnings", callback_data="mgmt_earnings"))

        management_bot.edit_message_text(
            earnings_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing daily earnings: {e}")
        management_bot.answer_callback_query(call.id, "Error loading daily earnings")

def show_weekly_earnings(call):
    """Show weekly earnings - placeholder for now"""
    management_bot.answer_callback_query(call.id, "Weekly earnings - Coming soon!")

def show_monthly_earnings(call):
    """Show monthly earnings - placeholder for now"""
    management_bot.answer_callback_query(call.id, "Monthly earnings - Coming soon!")

def show_delivery_fee_sharing(call):
    """Show delivery fee sharing breakdown"""
    try:
        earnings_data = calculate_personnel_earnings()
        personnel_data = get_data("delivery_personnel") or {}

        if not earnings_data:
            management_bot.edit_message_text(
                "🚚 *Delivery Fee Sharing*\n\nNo delivery fee data available.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return

        sharing_text = "🚚 *Delivery Fee Sharing (50/50)*\n\n"

        total_fees = 0
        total_personnel_share = 0

        for personnel_id, earnings in earnings_data.items():
            personnel_info = personnel_data.get(personnel_id, {})
            name = personnel_info.get('name', 'Unknown')

            fees = earnings['total_delivery_fees']
            share = earnings['total_earnings']

            total_fees += fees
            total_personnel_share += share

            sharing_text += (
                f"👤 *{name}*\n"
                f"🚚 Total Fees: {fees:.2f} birr\n"
                f"💰 Personnel Share: {share:.2f} birr\n"
                f"🏢 Company Share: {(fees - share):.2f} birr\n\n"
            )

        sharing_text += (
            f"📊 *Summary*\n"
            f"🚚 Total Delivery Fees: {total_fees:.2f} birr\n"
            f"👥 Total Personnel Share: {total_personnel_share:.2f} birr\n"
            f"🏢 Total Company Share: {(total_fees - total_personnel_share):.2f} birr"
        )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Earnings", callback_data="mgmt_earnings"))

        management_bot.edit_message_text(
            sharing_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing delivery fee sharing: {e}")
        management_bot.answer_callback_query(call.id, "Error loading delivery fee sharing")

# ============================================================================
# PERFORMANCE FUNCTIONS
# ============================================================================

def show_top_performers(call):
    """Show top performing delivery personnel"""
    try:
        earnings_data = calculate_personnel_earnings()
        personnel_data = get_data("delivery_personnel") or {}

        if not earnings_data:
            management_bot.edit_message_text(
                "🏆 *Top Performers*\n\nNo performance data available.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
            management_bot.answer_callback_query(call.id)
            return

        # Sort by total deliveries
        sorted_performers = sorted(
            earnings_data.items(),
            key=lambda x: x[1]['total_deliveries'],
            reverse=True
        )

        performance_text = "🏆 *Top Performers (Last 30 Days)*\n\n"

        for i, (personnel_id, earnings) in enumerate(sorted_performers[:5], 1):
            personnel_info = personnel_data.get(personnel_id, {})
            name = personnel_info.get('name', 'Unknown')

            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."

            performance_text += (
                f"{medal} *{name}*\n"
                f"📦 Deliveries: {earnings['total_deliveries']}\n"
                f"💰 Earnings: {earnings['total_earnings']:.2f} birr\n\n"
            )

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 Back to Performance", callback_data="mgmt_performance"))

        management_bot.edit_message_text(
            performance_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode='Markdown'
        )
        management_bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error showing top performers: {e}")
        management_bot.answer_callback_query(call.id, "Error loading top performers")

def show_performance_statistics(call):
    """Show overall performance statistics"""
    management_bot.answer_callback_query(call.id, "Performance statistics - Coming soon!")

def show_delivery_times(call):
    """Show delivery time analytics"""
    management_bot.answer_callback_query(call.id, "Delivery times - Coming soon!")

def show_performance_ratings(call):
    """Show performance ratings"""
    management_bot.answer_callback_query(call.id, "Performance ratings - Coming soon!")

def show_performance_trends(call):
    """Show performance trends"""
    management_bot.answer_callback_query(call.id, "Performance trends - Coming soon!")

def show_performance_goals(call):
    """Show performance goals and targets"""
    management_bot.answer_callback_query(call.id, "Performance goals - Coming soon!")

# ============================================================================
# NOTIFICATION COMPATIBILITY
# ============================================================================

def send_order_notification(chat_id: str, message: str) -> bool:
    """
    DISABLED: Automatic order notification functionality has been removed per user request
    This function is kept for compatibility but does nothing
    """
    logger.info(f"Order notification skipped for chat {chat_id} - automatic notifications disabled")
    return True  # Return True to maintain existing code flow

# ============================================================================
# BOT INITIALIZATION
# ============================================================================

def start_management_bot():
    """Start the management bot"""
    logger.info("Starting Management Bot...")
    try:
        management_bot.infinity_polling(timeout=60, long_polling_timeout=60)
    except Exception as e:
        logger.error(f"Management bot error: {e}")
        raise

if __name__ == "__main__":
    start_management_bot()
