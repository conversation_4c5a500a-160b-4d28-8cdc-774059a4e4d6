#!/usr/bin/env python3
"""
Test script to verify management bot callback functionality
This script tests that inline keyboard buttons respond properly
"""

import os
import sys
import time
import logging
from threading import Thread

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_callback_handler_registration():
    """Test that callback handlers are properly registered"""
    try:
        logger.info("Testing callback handler registration...")
        from src.bots.management_bot_minimal import management_bot
        
        # Check callback handlers
        callback_handlers = management_bot.callback_query_handlers
        logger.info(f"Number of callback handlers registered: {len(callback_handlers)}")
        
        for i, handler in enumerate(callback_handlers):
            logger.info(f"Handler {i+1}: {handler}")
        
        if len(callback_handlers) > 0:
            logger.info("✅ Callback handlers are registered")
            return True
        else:
            logger.error("❌ No callback handlers found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing callback handlers: {e}")
        return False

def test_callback_functions_exist():
    """Test that all callback functions exist and are callable"""
    try:
        logger.info("Testing callback function existence...")
        from src.bots.management_bot_minimal import (
            handle_callbacks,
            handle_personnel_menu,
            handle_analytics_menu,
            handle_earnings_menu,
            handle_performance_menu,
            handle_refresh_data,
            handle_back_to_menu,
            handle_personnel_callbacks,
            handle_analytics_callbacks,
            handle_earnings_callbacks,
            handle_performance_callbacks
        )
        
        functions = [
            handle_callbacks,
            handle_personnel_menu,
            handle_analytics_menu,
            handle_earnings_menu,
            handle_performance_menu,
            handle_refresh_data,
            handle_back_to_menu,
            handle_personnel_callbacks,
            handle_analytics_callbacks,
            handle_earnings_callbacks,
            handle_performance_callbacks
        ]
        
        for func in functions:
            if callable(func):
                logger.info(f"✅ {func.__name__} is callable")
            else:
                logger.error(f"❌ {func.__name__} is not callable")
                return False
        
        logger.info("✅ All callback functions exist and are callable")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Missing callback function: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing callback functions: {e}")
        return False

def test_authorization_function():
    """Test that authorization function works"""
    try:
        logger.info("Testing authorization function...")
        from src.bots.management_bot_minimal import is_authorized
        
        # Test with authorized ID (from config)
        authorized_result = is_authorized(7729984017)
        logger.info(f"Authorization test for 7729984017: {authorized_result}")
        
        # Test with unauthorized ID
        unauthorized_result = is_authorized(123456789)
        logger.info(f"Authorization test for 123456789: {unauthorized_result}")
        
        if callable(is_authorized):
            logger.info("✅ Authorization function is working")
            return True
        else:
            logger.error("❌ Authorization function is not callable")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing authorization: {e}")
        return False

def test_bot_instance():
    """Test that bot instance is properly created"""
    try:
        logger.info("Testing bot instance...")
        from src.bots.management_bot_minimal import management_bot
        
        if management_bot:
            logger.info(f"✅ Management bot instance exists: {type(management_bot)}")
            logger.info(f"Bot token configured: {'Yes' if management_bot.token else 'No'}")
            return True
        else:
            logger.error("❌ Management bot instance is None")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing bot instance: {e}")
        return False

def test_inline_keyboard_creation():
    """Test that inline keyboards can be created"""
    try:
        logger.info("Testing inline keyboard creation...")
        from telebot import types
        
        # Create a test keyboard like the one in the management bot
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("👥 Personnel", callback_data="mgmt_personnel"),
            types.InlineKeyboardButton("📊 Analytics", callback_data="mgmt_analytics")
        )
        markup.add(
            types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings"),
            types.InlineKeyboardButton("📈 Performance", callback_data="mgmt_performance")
        )
        markup.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="mgmt_refresh")
        )
        
        if markup and len(markup.keyboard) > 0:
            logger.info(f"✅ Inline keyboard created with {len(markup.keyboard)} rows")
            for i, row in enumerate(markup.keyboard):
                logger.info(f"  Row {i+1}: {[btn.text for btn in row]}")
            return True
        else:
            logger.error("❌ Failed to create inline keyboard")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error creating inline keyboard: {e}")
        return False

def main():
    """Run all callback functionality tests"""
    logger.info("🧪 Starting Callback Functionality Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Bot Instance Test", test_bot_instance),
        ("Authorization Function Test", test_authorization_function),
        ("Callback Handler Registration", test_callback_handler_registration),
        ("Callback Functions Existence", test_callback_functions_exist),
        ("Inline Keyboard Creation", test_inline_keyboard_creation)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 CALLBACK FUNCTIONALITY TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All callback functionality tests passed!")
        logger.info("✅ Management bot should respond to button clicks")
        logger.info("✅ All callback handlers are properly registered")
        logger.info("✅ Authorization system is working")
        logger.info("✅ Inline keyboards can be created")
        logger.info("\n💡 Try testing the bot with: /menu command")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
