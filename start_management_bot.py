#!/usr/bin/env python3
"""
Wiz Aroma Management Bot Startup Script
Starts the management bot with proper initialization and error handling.
"""

import sys
import os
import signal
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config import logger, TEST_MODE
from src.management_firebase_setup import initialize_management_collections, validate_management_data
from src.bots.management_bot import start_management_bot

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"Received signal {signum}, shutting down management bot...")
    sys.exit(0)

def check_prerequisites():
    """Check if all prerequisites are met before starting the bot"""
    logger.info("Checking prerequisites...")
    
    # Check if Firebase is accessible
    try:
        from src.firebase_db import get_data
        test_data = get_data("delivery_personnel")
        logger.info("✅ Firebase connection successful")
    except Exception as e:
        logger.error(f"❌ Firebase connection failed: {e}")
        return False
    
    # Check if management collections exist
    validation = validate_management_data()
    if not validation or not all(validation.values()):
        logger.warning("⚠️ Management collections not properly initialized")
        logger.info("Attempting to initialize management collections...")
        
        if initialize_management_collections():
            logger.info("✅ Management collections initialized successfully")
        else:
            logger.error("❌ Failed to initialize management collections")
            return False
    else:
        logger.info("✅ Management collections validated successfully")
    
    # Check if required modules are available
    try:
        import telebot
        from src.data_models import DeliveryPersonnel
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_telegram_id
        logger.info("✅ All required modules available")
    except ImportError as e:
        logger.error(f"❌ Missing required module: {e}")
        return False
    
    return True

def main():
    """Main function to start the management bot"""
    print("🏢 Wiz Aroma Management Bot")
    print("=" * 50)
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Check if running in test mode
    if TEST_MODE:
        logger.warning("⚠️ Running in TEST MODE - Limited functionality")
        print("⚠️ TEST MODE: No actual Telegram functionality will be available")
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites check failed. Cannot start management bot.")
        print("❌ Prerequisites check failed. Please check the logs for details.")
        sys.exit(1)
    
    print("✅ Prerequisites check passed")
    print("🚀 Starting Management Bot...")
    
    try:
        # Start the management bot
        logger.info("Starting Wiz Aroma Management Bot...")
        start_management_bot()
        
    except KeyboardInterrupt:
        logger.info("Management bot stopped by user")
        print("\n👋 Management bot stopped by user")
        
    except Exception as e:
        logger.error(f"Management bot crashed: {e}")
        print(f"❌ Management bot crashed: {e}")
        
        # In production, you might want to restart the bot automatically
        if not TEST_MODE:
            logger.info("Attempting to restart in 10 seconds...")
            print("🔄 Attempting to restart in 10 seconds...")
            time.sleep(10)
            main()  # Recursive restart
        else:
            sys.exit(1)

def test_management_bot():
    """Test management bot functionality without starting the polling loop"""
    print("🧪 Testing Management Bot Functionality")
    print("=" * 50)
    
    try:
        # Test Firebase collections
        print("Testing Firebase collections...")
        validation = validate_management_data()
        if validation and all(validation.values()):
            print("✅ Firebase collections: OK")
        else:
            print(f"⚠️ Firebase collections: {validation}")
        
        # Test bot initialization
        print("Testing bot initialization...")
        from src.bots.management_bot import management_bot
        bot_info = management_bot.get_me()
        print(f"✅ Bot initialization: OK (@{bot_info.username})")
        
        # Test analytics functions
        print("Testing analytics functions...")
        from src.bots.management_bot import calculate_daily_profits, calculate_personnel_earnings
        
        daily_profits = calculate_daily_profits()
        print(f"✅ Daily profits calculation: OK (found {daily_profits.get('total_orders', 0)} orders)")
        
        personnel_earnings = calculate_personnel_earnings()
        print(f"✅ Personnel earnings calculation: OK (found {len(personnel_earnings)} personnel)")
        
        print("\n🎉 All tests passed! Management bot is ready to use.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Management bot test failed: {e}")
        return False

def show_help():
    """Show help information"""
    help_text = """
🏢 Wiz Aroma Management Bot

Usage:
    python start_management_bot.py [command]

Commands:
    start       Start the management bot (default)
    test        Test management bot functionality
    init        Initialize management Firebase collections
    validate    Validate management data integrity
    help        Show this help message

Examples:
    python start_management_bot.py
    python start_management_bot.py test
    python start_management_bot.py init

Features:
    👥 Delivery Personnel Management
    📊 Analytics & Reporting  
    💰 Earnings Calculation
    📈 Performance Tracking

For more information, check the documentation or contact the system administrator.
    """
    print(help_text)

if __name__ == "__main__":
    # Parse command line arguments
    command = sys.argv[1] if len(sys.argv) > 1 else "start"
    
    if command == "start":
        main()
    elif command == "test":
        success = test_management_bot()
        sys.exit(0 if success else 1)
    elif command == "init":
        print("🔧 Initializing Management Collections...")
        success = initialize_management_collections()
        if success:
            print("✅ Management collections initialized successfully!")
        else:
            print("❌ Failed to initialize management collections")
        sys.exit(0 if success else 1)
    elif command == "validate":
        print("🔍 Validating Management Data...")
        validation = validate_management_data()
        if validation and all(validation.values()):
            print("✅ All management data is valid!")
            print(f"Validation results: {validation}")
        else:
            print("⚠️ Management data validation issues detected")
            print(f"Validation results: {validation}")
        sys.exit(0 if validation and all(validation.values()) else 1)
    elif command == "help":
        show_help()
    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'python start_management_bot.py help' for available commands")
        sys.exit(1)
