#!/usr/bin/env python3
"""
Simple test without loading data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simple_test():
    """Simple test"""
    print("🚀 SIMPLE TEST")
    print("=" * 30)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity, delivery_personnel_zones
        print(f"✅ Imports successful")
        
        print(f"📊 Current data in memory:")
        print(f"  Personnel: {len(delivery_personnel)} records")
        print(f"  Availability: {len(delivery_personnel_availability)} records")
        print(f"  Capacity: {len(delivery_personnel_capacity)} records")
        print(f"  Zones: {len(delivery_personnel_zones)} records")
        
        # Test Firebase connection
        print(f"\n🔥 Testing Firebase connection...")
        from src.firebase_db import get_data
        personnel_fb = get_data("delivery_personnel") or {}
        print(f"✅ Firebase connection successful - {len(personnel_fb)} personnel in Firebase")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    simple_test()
