#!/usr/bin/env python3
"""
Direct test of management bot functionality
"""

import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Test management bot directly"""
    logger.info("🧪 Testing Management Bot Callback Functionality")
    logger.info("=" * 60)
    
    try:
        # Test 1: Check if we can start the management bot
        logger.info("🔍 Test 1: Starting management bot...")
        
        # Set environment variable to avoid hanging
        os.environ['TEST_MODE'] = 'true'
        
        # Import and test
        import sys
        sys.path.insert(0, 'src')
        
        logger.info("✅ Import path set")
        
        # Try to run the management bot with a timeout
        logger.info("🚀 Starting management bot...")
        logger.info("💡 To test callback functionality:")
        logger.info("   1. Send /menu command to the bot")
        logger.info("   2. Click on any button (Personnel, Analytics, etc.)")
        logger.info("   3. The bot should respond immediately")
        logger.info("   4. If buttons don't respond, there's still an issue")
        
        # Start the bot
        os.system("python main.py --management")
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
