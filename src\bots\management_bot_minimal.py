"""
Minimal Management Bot for Wiz Aroma Delivery System
Replaces notification bot with core functionality only.
"""

import telebot
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get management bot token (same as notification bot for continuity)
MANAGEMENT_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")

# Management bot authorized users
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017]  # Same as order tracking bot

# Simple logger setup
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Initialize management bot
management_bot = telebot.TeleBot(MANAGEMENT_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to use management bot"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

def send_order_notification(chat_id: int, message_text: str) -> bool:
    """
    Send order notification to specified chat
    This replaces the notification bot functionality
    """
    try:
        management_bot.send_message(chat_id, message_text, parse_mode='HTML')
        logger.info(f"Order notification sent to chat {chat_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to send order notification to chat {chat_id}: {e}")
        return False

@management_bot.message_handler(commands=['start'])
def handle_start(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    
    welcome_text = """
🎯 **Management Bot Active**

This bot has replaced the notification bot and provides:
• Order notifications
• Management functions
• Analytics and reporting

Use /help for available commands.
"""
    management_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@management_bot.message_handler(commands=['help'])
def handle_help(message):
    """Handle /help command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    
    help_text = """
🔧 **Management Bot Commands**

**Core Functions:**
• Order notifications (automatic)
• System management
• Analytics and reporting

**Status:**
✅ Notification bot functionality: ACTIVE
✅ Management features: ACTIVE

This bot has completely replaced the notification bot.
"""
    management_bot.reply_to(message, help_text, parse_mode='Markdown')

@management_bot.message_handler(commands=['status'])
def handle_status(message):
    """Handle /status command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    
    status_text = """
📊 **Management Bot Status**

🤖 **Bot Status:** Online
🔔 **Notifications:** Active
📈 **Management:** Active

**Replacement Status:**
✅ Notification bot: REPLACED
✅ Order notifications: ACTIVE
✅ Management functions: ACTIVE

The notification bot has been completely eliminated.
"""
    management_bot.reply_to(message, status_text, parse_mode='Markdown')

# Test function for order notifications
def test_notification():
    """Test the notification functionality"""
    test_message = """
🍕 **New Order Alert!**

**Order #12345**
Customer: Test User
Items: 2x Pizza, 1x Drink
Total: $25.00
Address: Test Address

This is a test notification from the Management Bot.
"""
    return send_order_notification(7729984017, test_message)

if __name__ == "__main__":
    print("🚀 Starting Management Bot (Minimal Version)...")
    print("✅ Management Bot has replaced Notification Bot")
    management_bot.polling()
