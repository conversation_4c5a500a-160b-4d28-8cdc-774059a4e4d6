"""
Minimal Management Bot for Wiz Aroma Delivery System
Replaces notification bot with core functionality only.
"""

import telebot
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get management bot token (same as notification bot for continuity)
MANAGEMENT_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")

# Management bot authorized users
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017]  # Same as order tracking bot

# Simple logger setup
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Initialize management bot
management_bot = telebot.TeleBot(MANAGEMENT_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to use management bot"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

# REMOVED: send_order_notification function
# The management bot no longer sends automatic order notifications
# This functionality has been completely eliminated per user request

@management_bot.message_handler(commands=['start'])
def handle_start(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    
    welcome_text = """
🎯 **Management Bot Active**

This bot provides management functions only:
• Personnel management
• Analytics and reporting
• System administration

⚠️ **Note:** Automatic order notifications have been disabled.

Use /help for available commands.
"""
    management_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@management_bot.message_handler(commands=['help'])
def handle_help(message):
    """Handle /help command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    
    help_text = """
🔧 **Management Bot Commands**

**Available Commands:**
• /start - Show welcome message
• /help - Show this help message
• /status - Show bot status
• /menu - Access management functions

**Core Functions:**
• Personnel management
• Analytics and reporting
• System administration

**Status:**
❌ Automatic notifications: DISABLED
✅ Management features: ACTIVE

Use /menu to access management functions.
"""
    management_bot.reply_to(message, help_text, parse_mode='Markdown')

@management_bot.message_handler(commands=['status'])
def handle_status(message):
    """Handle /status command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    
    status_text = """
📊 **Management Bot Status**

🤖 **Bot Status:** Online
❌ **Automatic Notifications:** DISABLED
✅ **Management Functions:** Active

**Configuration:**
✅ Notification bot: REPLACED
❌ Order notifications: DISABLED (per user request)
✅ Management features: ACTIVE

Use /menu to access management functions.
"""
    management_bot.reply_to(message, status_text, parse_mode='Markdown')

@management_bot.message_handler(commands=['menu'])
def handle_menu(message):
    """Handle /menu command"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return

    # Import inline keyboard types
    from telebot import types

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👥 Personnel", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics", callback_data="mgmt_analytics")
    )
    markup.add(
        types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings"),
        types.InlineKeyboardButton("📈 Performance", callback_data="mgmt_performance")
    )
    markup.add(
        types.InlineKeyboardButton("🔄 Refresh", callback_data="mgmt_refresh")
    )

    menu_text = """
🏢 **Management Dashboard**

Select a management area:

👥 **Personnel** - Manage delivery staff
📊 **Analytics** - View reports and data
💰 **Earnings** - Financial tracking
📈 **Performance** - Staff performance metrics
🔄 **Refresh** - Update all data
"""

    management_bot.reply_to(message, menu_text, reply_markup=markup, parse_mode='Markdown')

# ============================================================================
# CALLBACK HANDLERS
# ============================================================================

@management_bot.callback_query_handler(func=lambda call: True)
def handle_callbacks(call):
    """Handle all callback queries"""
    user_id = call.from_user.id

    # Always answer the callback query first to remove loading state
    try:
        management_bot.answer_callback_query(call.id)
    except Exception as e:
        logger.error(f"Error answering callback query: {e}")

    if not is_authorized(user_id):
        try:
            management_bot.send_message(user_id, "❌ You are not authorized to use this bot.")
        except:
            pass
        return

    try:
        logger.info(f"Processing callback: {call.data} from user {user_id}")

        if call.data == "mgmt_personnel":
            handle_personnel_menu(call)
        elif call.data == "mgmt_analytics":
            handle_analytics_menu(call)
        elif call.data == "mgmt_earnings":
            handle_earnings_menu(call)
        elif call.data == "mgmt_performance":
            handle_performance_menu(call)
        elif call.data == "mgmt_refresh":
            handle_refresh_data(call)
        elif call.data == "back_to_menu":
            handle_back_to_menu(call)
        elif call.data.startswith("personnel_"):
            handle_personnel_callbacks(call)
        elif call.data.startswith("analytics_"):
            handle_analytics_callbacks(call)
        elif call.data.startswith("earnings_"):
            handle_earnings_callbacks(call)
        elif call.data.startswith("performance_"):
            handle_performance_callbacks(call)
        else:
            # Handle unknown callbacks
            logger.warning(f"Unknown callback data: {call.data}")
            try:
                management_bot.edit_message_text(
                    f"⚠️ **Unknown Action**\n\nCallback '{call.data}' is not implemented yet.\n\nUse /menu to return to the main menu.",
                    call.message.chat.id,
                    call.message.message_id,
                    parse_mode='Markdown'
                )
            except Exception as edit_error:
                logger.error(f"Error editing message for unknown callback: {edit_error}")
                management_bot.send_message(
                    call.message.chat.id,
                    f"⚠️ Unknown action: {call.data}\n\nUse /menu to return to the main menu."
                )

    except Exception as e:
        logger.error(f"Error handling callback {call.data}: {e}")
        try:
            management_bot.edit_message_text(
                "❌ **Error**\n\nAn error occurred processing your request.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        except Exception as edit_error:
            logger.error(f"Error editing message for error callback: {edit_error}")
            try:
                management_bot.send_message(
                    call.message.chat.id,
                    "❌ Error occurred. Use /menu to access management functions."
                )
            except Exception as send_error:
                logger.error(f"Error sending error message: {send_error}")

# ============================================================================
# MENU HANDLER FUNCTIONS
# ============================================================================

def handle_personnel_menu(call):
    """Handle personnel management menu"""
    from telebot import types

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("➕ Add Staff", callback_data="personnel_add"),
        types.InlineKeyboardButton("👥 View All", callback_data="personnel_view")
    )
    markup.add(
        types.InlineKeyboardButton("📊 Statistics", callback_data="personnel_stats"),
        types.InlineKeyboardButton("🔍 Search", callback_data="personnel_search")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back", callback_data="back_to_menu")
    )

    personnel_text = """
👥 **Personnel Management**

Manage your delivery staff:

➕ **Add Staff** - Register new delivery personnel
👥 **View All** - See all registered staff
📊 **Statistics** - View personnel statistics
🔍 **Search** - Find specific personnel

⚠️ **Note:** Personnel management features are currently in development.
"""

    management_bot.edit_message_text(
        personnel_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=markup,
        parse_mode='Markdown'
    )

def handle_analytics_menu(call):
    """Handle analytics menu"""
    from telebot import types

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("📅 Daily", callback_data="analytics_daily"),
        types.InlineKeyboardButton("📊 Weekly", callback_data="analytics_weekly")
    )
    markup.add(
        types.InlineKeyboardButton("📈 Monthly", callback_data="analytics_monthly"),
        types.InlineKeyboardButton("📋 Reports", callback_data="analytics_reports")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back", callback_data="back_to_menu")
    )

    analytics_text = """
📊 **Analytics & Reports**

View system analytics:

📅 **Daily** - Today's performance
📊 **Weekly** - Weekly summaries
📈 **Monthly** - Monthly reports
📋 **Reports** - Detailed reports

⚠️ **Note:** Analytics features are currently in development.
"""

    management_bot.edit_message_text(
        analytics_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=markup,
        parse_mode='Markdown'
    )

def handle_earnings_menu(call):
    """Handle earnings menu"""
    from telebot import types

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("💰 Total", callback_data="earnings_total"),
        types.InlineKeyboardButton("👥 Personnel", callback_data="earnings_personnel")
    )
    markup.add(
        types.InlineKeyboardButton("📅 Daily", callback_data="earnings_daily"),
        types.InlineKeyboardButton("📊 Weekly", callback_data="earnings_weekly")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back", callback_data="back_to_menu")
    )

    earnings_text = """
💰 **Earnings & Financial Tracking**

Track financial performance:

💰 **Total** - Overall earnings
👥 **Personnel** - Staff earnings
📅 **Daily** - Daily breakdown
📊 **Weekly** - Weekly summaries

⚠️ **Note:** Earnings tracking features are currently in development.
"""

    management_bot.edit_message_text(
        earnings_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=markup,
        parse_mode='Markdown'
    )

def handle_performance_menu(call):
    """Handle performance menu"""
    from telebot import types

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("🏆 Top Performers", callback_data="performance_top"),
        types.InlineKeyboardButton("📊 Statistics", callback_data="performance_stats")
    )
    markup.add(
        types.InlineKeyboardButton("⏱️ Delivery Times", callback_data="performance_times"),
        types.InlineKeyboardButton("⭐ Ratings", callback_data="performance_ratings")
    )
    markup.add(
        types.InlineKeyboardButton("🔙 Back", callback_data="back_to_menu")
    )

    performance_text = """
📈 **Performance Tracking**

Monitor staff performance:

🏆 **Top Performers** - Best performing staff
📊 **Statistics** - Performance metrics
⏱️ **Delivery Times** - Time analytics
⭐ **Ratings** - Customer ratings

⚠️ **Note:** Performance tracking features are currently in development.
"""

    management_bot.edit_message_text(
        performance_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=markup,
        parse_mode='Markdown'
    )

def handle_refresh_data(call):
    """Handle data refresh"""
    from telebot import types
    import datetime

    # Show refreshing message
    management_bot.edit_message_text(
        "🔄 **Refreshing Data...**\n\nPlease wait while we update all management data.",
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Simulate data refresh (in a real implementation, this would refresh from Firebase)
    import time
    time.sleep(2)  # Simulate processing time

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_menu"))

    refresh_text = f"""
✅ **Data Refresh Complete**

🕐 **Last Updated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 **Status:**
✅ Personnel data refreshed
✅ Analytics data updated
✅ Earnings data synchronized
✅ Performance metrics updated

All management data has been refreshed successfully.
"""

    management_bot.edit_message_text(
        refresh_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=markup,
        parse_mode='Markdown'
    )

def handle_back_to_menu(call):
    """Handle back to main menu"""
    from telebot import types

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👥 Personnel", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics", callback_data="mgmt_analytics")
    )
    markup.add(
        types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings"),
        types.InlineKeyboardButton("📈 Performance", callback_data="mgmt_performance")
    )
    markup.add(
        types.InlineKeyboardButton("🔄 Refresh", callback_data="mgmt_refresh")
    )

    menu_text = """
🏢 **Management Dashboard**

Select a management area:

👥 **Personnel** - Manage delivery staff
📊 **Analytics** - View reports and data
💰 **Earnings** - Financial tracking
📈 **Performance** - Staff performance metrics
🔄 **Refresh** - Update all data
"""

    management_bot.edit_message_text(
        menu_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=markup,
        parse_mode='Markdown'
    )

# ============================================================================
# ADDITIONAL CALLBACK HANDLERS
# ============================================================================

def handle_personnel_callbacks(call):
    """Handle personnel-related callbacks"""
    try:
        if call.data == "personnel_add":
            management_bot.edit_message_text(
                "➕ **Add Personnel**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "personnel_view":
            management_bot.edit_message_text(
                "👥 **View All Personnel**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "personnel_stats":
            management_bot.edit_message_text(
                "📊 **Personnel Statistics**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "personnel_search":
            management_bot.edit_message_text(
                "🔍 **Search Personnel**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        else:
            management_bot.edit_message_text(
                f"⚠️ **Unknown Personnel Action**\n\nAction '{call.data}' is not implemented.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
    except Exception as e:
        logger.error(f"Error handling personnel callback {call.data}: {e}")

def handle_analytics_callbacks(call):
    """Handle analytics-related callbacks"""
    try:
        if call.data == "analytics_daily":
            management_bot.edit_message_text(
                "📅 **Daily Analytics**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "analytics_weekly":
            management_bot.edit_message_text(
                "📊 **Weekly Analytics**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "analytics_monthly":
            management_bot.edit_message_text(
                "📈 **Monthly Analytics**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "analytics_reports":
            management_bot.edit_message_text(
                "📋 **Detailed Reports**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        else:
            management_bot.edit_message_text(
                f"⚠️ **Unknown Analytics Action**\n\nAction '{call.data}' is not implemented.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
    except Exception as e:
        logger.error(f"Error handling analytics callback {call.data}: {e}")

def handle_earnings_callbacks(call):
    """Handle earnings-related callbacks"""
    try:
        if call.data == "earnings_total":
            management_bot.edit_message_text(
                "💰 **Total Earnings**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "earnings_personnel":
            management_bot.edit_message_text(
                "👥 **Personnel Earnings**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "earnings_daily":
            management_bot.edit_message_text(
                "📅 **Daily Earnings**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "earnings_weekly":
            management_bot.edit_message_text(
                "📊 **Weekly Earnings**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        else:
            management_bot.edit_message_text(
                f"⚠️ **Unknown Earnings Action**\n\nAction '{call.data}' is not implemented.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
    except Exception as e:
        logger.error(f"Error handling earnings callback {call.data}: {e}")

def handle_performance_callbacks(call):
    """Handle performance-related callbacks"""
    try:
        if call.data == "performance_top":
            management_bot.edit_message_text(
                "🏆 **Top Performers**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "performance_stats":
            management_bot.edit_message_text(
                "📊 **Performance Statistics**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "performance_times":
            management_bot.edit_message_text(
                "⏱️ **Delivery Times**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        elif call.data == "performance_ratings":
            management_bot.edit_message_text(
                "⭐ **Performance Ratings**\n\nThis feature is currently in development.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        else:
            management_bot.edit_message_text(
                f"⚠️ **Unknown Performance Action**\n\nAction '{call.data}' is not implemented.\n\nUse /menu to return to the main menu.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
    except Exception as e:
        logger.error(f"Error handling performance callback {call.data}: {e}")

if __name__ == "__main__":
    print("🚀 Starting Management Bot (Minimal Version)...")
    print("✅ Management Bot has replaced Notification Bot")
    management_bot.polling()
