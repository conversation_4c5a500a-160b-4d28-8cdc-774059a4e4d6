#!/usr/bin/env python3
"""
Simple test to check if callback handler is working
"""

import sys
sys.path.insert(0, 'src')

def test_handler_import():
    """Test if we can import the handler function directly"""
    print("🔍 Testing direct handler import...")
    
    try:
        # Import the handler function directly
        from src.handlers.order_handlers import handle_order_not_received
        print("✅ Successfully imported handle_order_not_received function")
        
        # Check if it's callable
        if callable(handle_order_not_received):
            print("✅ Function is callable")
        else:
            print("❌ Function is not callable")
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import handler: {e}")
        return False
    except Exception as e:
        print(f"❌ Error importing handler: {e}")
        return False

def test_delivery_personnel_function():
    """Test the delivery personnel lookup function"""
    print("\n🔍 Testing delivery personnel function...")
    
    try:
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        print("✅ Successfully imported get_delivery_personnel_by_id")
        
        # Test with a sample ID
        result = get_delivery_personnel_by_id("test_id")
        print(f"📋 Function returned: {type(result)} - {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import function: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing function: {e}")
        return False

def test_firebase_access():
    """Test Firebase access"""
    print("\n🔍 Testing Firebase access...")
    
    try:
        from src.firebase_db import get_data
        
        # Test getting confirmed orders
        confirmed_orders = get_data("confirmed_orders")
        if confirmed_orders:
            print(f"✅ Found {len(confirmed_orders)} confirmed orders")
            
            # Get a sample order to test structure
            sample_order_key = list(confirmed_orders.keys())[0]
            sample_order = confirmed_orders[sample_order_key]
            
            print(f"📄 Sample order structure:")
            print(f"   - Order: {sample_order_key}")
            print(f"   - assigned_to: {sample_order.get('assigned_to', 'N/A')}")
            print(f"   - delivery_status: {sample_order.get('delivery_status', 'N/A')}")
            print(f"   - user_id: {sample_order.get('user_id', 'N/A')}")
            
            return True
        else:
            print("❌ No confirmed orders found")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing Firebase: {e}")
        return False

def main():
    """Run simple tests"""
    print("🚀 Running simple callback handler tests...\n")
    
    tests = [
        ("Handler Import", test_handler_import),
        ("Delivery Personnel Function", test_delivery_personnel_function),
        ("Firebase Access", test_firebase_access),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*40)
    print("📊 SIMPLE TEST RESULTS")
    print("="*40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")

if __name__ == "__main__":
    main()
