#!/usr/bin/env python
"""
Script to clear webhooks for all bots to resolve polling conflicts.
"""

import os
import sys
import telebot

# Get bot tokens directly from .env file
BOT_TOKEN = "7829620588:AAH4CMNGfAI2GnJnzo3irp_r_NXDZ7XxWe0"
ADMIN_BOT_TOKEN = "8066731052:AAGxNFr5etni9Zxtu0HLV3yyiRKWFXMgzwc"
FINANCE_BOT_TOKEN = "7649115266:AAFaixkbxPAWYuxpvGxdG0aj5ra6Jxs1b6Q"
MAINTENANCE_BOT_TOKEN = "8000905218:AAEaQ8zBEZ8XJ2LEeY6WXQxQNNbjKck3K-E"
NOTIFICATION_BOT_TOKEN = "8187447788:AAHh_MU3EPnyCz29aYbnPLERUUkPpxyGlrA"

def clear_webhook(token, bot_name):
    """Clear webhook for a specific bot"""
    try:
        bot = telebot.TeleBot(token)
        result = bot.remove_webhook()
        print(f"✅ {bot_name}: Webhook cleared successfully - {result}")
        return True
    except Exception as e:
        print(f"❌ {bot_name}: Error clearing webhook - {e}")
        return False

def main():
    """Clear webhooks for all bots"""
    print("🔧 Clearing webhooks for all bots...")
    
    bots = [
        (BOT_TOKEN, "User Bot"),
        (ADMIN_BOT_TOKEN, "Admin Bot"),
        (FINANCE_BOT_TOKEN, "Finance Bot"),
        (MAINTENANCE_BOT_TOKEN, "Maintenance Bot"),
        (NOTIFICATION_BOT_TOKEN, "Notification Bot"),
    ]
    
    success_count = 0
    for token, name in bots:
        if token:
            if clear_webhook(token, name):
                success_count += 1
        else:
            print(f"⚠️ {name}: Token not found")
    
    print(f"\n📊 Summary: {success_count}/{len(bots)} webhooks cleared successfully")
    
    if success_count == len(bots):
        print("✅ All webhooks cleared! You can now run the bots with polling.")
    else:
        print("⚠️ Some webhooks could not be cleared. Check the errors above.")

if __name__ == "__main__":
    main()
