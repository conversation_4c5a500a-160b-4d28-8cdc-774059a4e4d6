# 🏢 Wiz Aroma Management Bot

## Overview

The Management Bot is a comprehensive administrative system for the Wiz Aroma delivery platform. It transforms the existing Notification Bot into a powerful management interface with delivery personnel management, analytics, reporting, and earnings calculation capabilities.

## 🚀 Features

### 👥 Delivery Personnel Management
- **Add Personnel**: Register new delivery personnel with Telegram ID integration
- **Remove Personnel**: Safely remove personnel from the system
- **View All Personnel**: Display comprehensive personnel list with status
- **Edit Personnel**: Update personnel information and settings
- **Search Personnel**: Find personnel by Telegram ID or name
- **Personnel Statistics**: View performance metrics and statistics

### 📊 Analytics & Reporting
- **Daily Analytics**: Comprehensive daily profit and order summaries
- **Weekly Reports**: 7-day performance analysis
- **Monthly Reports**: Monthly business intelligence
- **Transaction Count**: Order volume tracking across time periods
- **Delivery Reports**: Assignment success rates and performance
- **Profit Summary**: Revenue and profit calculations

### 💰 Earnings & Profits
- **Total Profits**: All-time business performance
- **Personnel Earnings**: Individual delivery personnel earnings (50% sharing)
- **Daily/Weekly/Monthly Earnings**: Time-based earnings breakdown
- **Delivery Fee Sharing**: Transparent 50/50 fee distribution tracking

### 📈 Performance Tracking
- **Top Performers**: Leaderboard of best delivery personnel
- **Performance Statistics**: Comprehensive performance metrics
- **Delivery Times**: Time-based performance analysis
- **Ratings & Reviews**: Quality tracking (planned)
- **Trends Analysis**: Performance trend identification (planned)

## 🔧 Technical Architecture

### Bot Configuration
- **Token**: Uses existing `NOTIFICATION_BOT_TOKEN` for seamless transition
- **Access Control**: Restricted to authorized Telegram IDs
- **Compatibility**: Maintains notification functionality while adding management features

### Firebase Integration
The management bot uses dedicated Firebase collections:

```
management_collections/
├── profit_summaries/
│   ├── daily/
│   ├── weekly/
│   └── monthly/
├── personnel_earnings/
│   ├── current_month/
│   └── historical/
├── management_reports/
│   ├── analytics/
│   ├── performance/
│   └── earnings/
└── management_settings/
    ├── delivery_fee_share_percentage: 50
    ├── profit_calculation_method: "revenue_plus_fees"
    └── currency: "birr"
```

### Key Components

1. **Management Bot Core** (`src/bots/management_bot.py`)
   - Main bot interface with menu system
   - Command handlers and callback management
   - Access control and authorization

2. **Analytics Engine**
   - Daily/weekly/monthly profit calculations
   - Personnel earnings calculation (50% sharing)
   - Transaction counting and reporting

3. **Firebase Setup** (`src/management_firebase_setup.py`)
   - Collection initialization
   - Data validation
   - Export/import functionality

4. **Startup Script** (`start_management_bot.py`)
   - Bot initialization and error handling
   - Prerequisites checking
   - Testing and validation commands

## 🚀 Getting Started

### Prerequisites
- Firebase Realtime Database access
- Telegram bot token (uses existing notification bot token)
- Python dependencies (telebot, firebase-admin)

### Installation

1. **Initialize Management Collections**:
   ```bash
   python start_management_bot.py init
   ```

2. **Validate Setup**:
   ```bash
   python start_management_bot.py validate
   ```

3. **Test Functionality**:
   ```bash
   python start_management_bot.py test
   ```

4. **Start Management Bot**:
   ```bash
   python start_management_bot.py
   ```

### Access Control

The management bot is restricted to authorized users defined in `ORDER_TRACK_BOT_AUTHORIZED_IDS`:
- Default authorized ID: `7729984017`
- Additional IDs can be added in `src/config.py`

## 📱 Usage

### Main Menu
Access the management dashboard with `/menu` command:

```
🏢 Management Dashboard

👥 Personnel Management    📊 Analytics & Reports
💰 Earnings & Profits     📈 Performance Stats
🔄 Refresh Data
```

### Personnel Management
- **Add Personnel**: Provide name, phone, Telegram ID, and service areas
- **View All**: See complete personnel list with status indicators
- **Statistics**: View personnel performance metrics

### Analytics
- **Daily Reports**: Today's orders, revenue, and profit
- **Transaction Count**: Order volume across time periods
- **Delivery Reports**: Assignment success rates

### Earnings
- **Personnel Earnings**: Individual earnings with 50% delivery fee sharing
- **Profit Summary**: Business performance overview
- **Delivery Fee Sharing**: Transparent fee distribution

## 🔄 Migration from Notification Bot

The management bot seamlessly replaces the notification bot while maintaining compatibility:

1. **Same Token**: Uses `NOTIFICATION_BOT_TOKEN` for continuity
2. **Preserved Functionality**: Maintains `send_order_notification()` function
3. **Enhanced Features**: Adds comprehensive management capabilities
4. **No Disruption**: Existing integrations continue to work

## 📊 Analytics Calculations

### Profit Calculation
```python
total_profit = total_revenue + total_delivery_fees
```

### Personnel Earnings (50% Sharing)
```python
personnel_share = delivery_fee * 0.5
company_share = delivery_fee * 0.5
```

### Performance Metrics
- **Success Rate**: `completed_deliveries / total_assignments * 100`
- **Average Earnings**: `total_earnings / total_deliveries`
- **Daily Average**: `monthly_total / 30`

## 🛠️ Development

### Adding New Features

1. **New Analytics**: Add functions to analytics section
2. **New Reports**: Extend reporting capabilities
3. **New Menus**: Add callback handlers and menu options

### Testing
```bash
# Test all functionality
python start_management_bot.py test

# Validate data integrity
python start_management_bot.py validate
```

### Debugging
- Check logs for detailed error information
- Use test mode for development: `TEST_MODE=True`
- Validate Firebase collections regularly

## 🔒 Security

- **Access Control**: Telegram ID-based authorization
- **Data Validation**: Input sanitization and validation
- **Error Handling**: Comprehensive exception management
- **Audit Trail**: All actions logged for accountability

## 📈 Future Enhancements

### Planned Features
- **Advanced Analytics**: Trend analysis and forecasting
- **Performance Goals**: Target setting and tracking
- **Automated Reports**: Scheduled report generation
- **Mobile Dashboard**: Web interface for management
- **Integration APIs**: External system integration

### Scalability
- **Database Optimization**: Efficient data queries
- **Caching**: Performance optimization
- **Load Balancing**: Multi-instance support
- **Monitoring**: Health checks and alerts

## 🆘 Support

### Common Issues
1. **Bot Not Responding**: Check token and network connectivity
2. **Data Not Loading**: Validate Firebase collections
3. **Access Denied**: Verify Telegram ID authorization

### Troubleshooting
```bash
# Check prerequisites
python start_management_bot.py validate

# Test functionality
python start_management_bot.py test

# Reinitialize collections
python start_management_bot.py init
```

### Contact
For technical support or feature requests, contact the development team or check the project documentation.

---

**Version**: 1.0.0  
**Last Updated**: 2025-01-06  
**Compatibility**: Wiz Aroma V1.3.3+
