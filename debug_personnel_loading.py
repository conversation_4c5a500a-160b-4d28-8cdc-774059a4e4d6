#!/usr/bin/env python3
"""
Debug delivery personnel data loading
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_personnel_loading():
    """Debug delivery personnel data loading"""
    print("=== DEBUGGING PERSONNEL DATA LOADING ===")
    
    # 1. Check Firebase data directly
    print("1. Checking Firebase data directly...")
    from src.firebase_db import get_data
    
    personnel_fb = get_data("delivery_personnel") or {}
    availability_fb = get_data("delivery_personnel_availability") or {}
    capacity_fb = get_data("delivery_personnel_capacity") or {}
    
    print(f"Firebase personnel count: {len(personnel_fb)}")
    print(f"Firebase availability count: {len(availability_fb)}")
    print(f"Firebase capacity count: {len(capacity_fb)}")
    
    target_id = "dp_31fe5be0"
    if target_id in personnel_fb:
        print(f"✅ {target_id} found in Firebase personnel")
        print(f"  Status: {personnel_fb[target_id].get('status')}")
        print(f"  Verified: {personnel_fb[target_id].get('is_verified')}")
        print(f"  Service Areas: {personnel_fb[target_id].get('service_areas')}")
    else:
        print(f"❌ {target_id} NOT found in Firebase personnel")
    
    if target_id in availability_fb:
        print(f"✅ {target_id} found in Firebase availability: {availability_fb[target_id]}")
    else:
        print(f"❌ {target_id} NOT found in Firebase availability")
    
    if target_id in capacity_fb:
        print(f"✅ {target_id} found in Firebase capacity: {capacity_fb[target_id]}")
    else:
        print(f"❌ {target_id} NOT found in Firebase capacity")
    
    # 2. Check data_models loading
    print(f"\n2. Checking data_models loading...")
    from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity
    
    print(f"data_models personnel count: {len(delivery_personnel)}")
    print(f"data_models availability count: {len(delivery_personnel_availability)}")
    print(f"data_models capacity count: {len(delivery_personnel_capacity)}")
    
    if target_id in delivery_personnel:
        print(f"✅ {target_id} found in data_models personnel")
        print(f"  Status: {delivery_personnel[target_id].get('status')}")
        print(f"  Verified: {delivery_personnel[target_id].get('is_verified')}")
        print(f"  Service Areas: {delivery_personnel[target_id].get('service_areas')}")
    else:
        print(f"❌ {target_id} NOT found in data_models personnel")
    
    if target_id in delivery_personnel_availability:
        print(f"✅ {target_id} found in data_models availability: {delivery_personnel_availability[target_id]}")
    else:
        print(f"❌ {target_id} NOT found in data_models availability")
    
    if target_id in delivery_personnel_capacity:
        print(f"✅ {target_id} found in data_models capacity: {delivery_personnel_capacity[target_id]}")
    else:
        print(f"❌ {target_id} NOT found in data_models capacity")
    
    # 3. Force reload data
    print(f"\n3. Force reloading data...")
    from src.data_storage import (
        load_delivery_personnel_data,
        load_delivery_personnel_availability_data,
        load_delivery_personnel_capacity_data
    )
    
    # Clear and reload
    delivery_personnel.clear()
    delivery_personnel_availability.clear()
    delivery_personnel_capacity.clear()
    
    delivery_personnel.update(load_delivery_personnel_data())
    delivery_personnel_availability.update(load_delivery_personnel_availability_data())
    delivery_personnel_capacity.update(load_delivery_personnel_capacity_data())
    
    print(f"After reload - personnel count: {len(delivery_personnel)}")
    print(f"After reload - availability count: {len(delivery_personnel_availability)}")
    print(f"After reload - capacity count: {len(delivery_personnel_capacity)}")
    
    if target_id in delivery_personnel:
        print(f"✅ {target_id} found after reload")
    else:
        print(f"❌ {target_id} still NOT found after reload")
    
    # 4. Test DeliveryPersonnel.is_available()
    print(f"\n4. Testing DeliveryPersonnel.is_available()...")
    if target_id in delivery_personnel:
        from src.data_models import DeliveryPersonnel
        
        personnel_data = delivery_personnel[target_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        
        print(f"Personnel object created:")
        print(f"  Status: {personnel.status}")
        print(f"  Verified: {personnel.is_verified}")
        print(f"  Current Capacity: {personnel.current_capacity}")
        print(f"  Max Capacity: {personnel.max_capacity}")
        print(f"  Service Areas: {personnel.service_areas}")
        
        is_available = personnel.is_available()
        print(f"  is_available(): {is_available}")
        
        # Check individual conditions
        print(f"  status == 'available': {personnel.status == 'available'}")
        print(f"  is_verified: {personnel.is_verified}")
        print(f"  current_capacity < max_capacity: {personnel.current_capacity < personnel.max_capacity}")
        
        # Test can_serve_area
        for area in ['1', '2', '3', '4', '5']:
            can_serve = personnel.can_serve_area(area)
            print(f"  can_serve_area('{area}'): {can_serve}")
    
    # 5. Test find_available_personnel with debug
    print(f"\n5. Testing find_available_personnel with debug...")
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        # Add debug prints to understand what's happening
        print(f"Testing area '1'...")
        
        available_personnel = []
        exclude_personnel = []
        area_id = "1"
        
        print(f"Checking {len(delivery_personnel)} personnel...")
        for personnel_id, personnel_data in delivery_personnel.items():
            print(f"\nChecking {personnel_id}:")
            
            # Skip excluded personnel
            if personnel_id in exclude_personnel:
                print(f"  SKIPPED: In exclude list")
                continue
            
            from src.data_models import DeliveryPersonnel
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            
            print(f"  is_available(): {personnel.is_available()}")
            print(f"  can_serve_area('{area_id}'): {personnel.can_serve_area(area_id)}")
            
            availability_status = delivery_personnel_availability.get(personnel_id)
            print(f"  availability_status: {availability_status}")
            print(f"  availability == 'available': {availability_status == 'available'}")
            
            # Check if personnel is available and can serve the area
            if (personnel.is_available() and 
                personnel.can_serve_area(area_id) and
                delivery_personnel_availability.get(personnel_id) == "available"):
                
                current_capacity = delivery_personnel_capacity.get(personnel_id, 0)
                print(f"  ✅ AVAILABLE - capacity: {current_capacity}")
                available_personnel.append({
                    "personnel_id": personnel_id,
                    "current_capacity": current_capacity,
                    "max_capacity": personnel.max_capacity,
                    "rating": personnel.rating,
                    "total_deliveries": personnel.total_deliveries
                })
            else:
                print(f"  ❌ NOT AVAILABLE")
        
        print(f"\nFound {len(available_personnel)} available personnel")
        for p in available_personnel:
            print(f"  {p['personnel_id']}: capacity {p['current_capacity']}/{p['max_capacity']}")
        
        return len(available_personnel) > 0
        
    except Exception as e:
        print(f"❌ Error in find_available_personnel debug: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_personnel_loading()
