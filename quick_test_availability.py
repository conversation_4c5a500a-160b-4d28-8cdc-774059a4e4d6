#!/usr/bin/env python3
"""
Quick test of delivery personnel availability
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_availability():
    """Test delivery personnel availability"""
    print("🚀 QUICK AVAILABILITY TEST")
    print("=" * 50)
    
    try:
        # Import required modules
        from src.data_storage import load_user_data
        from src.utils.delivery_personnel_utils import find_available_personnel
        from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity, delivery_personnel_zones
        
        print("📥 Loading data from Firebase...")
        load_user_data()
        
        print(f"📊 Data loaded:")
        print(f"  Personnel: {len(delivery_personnel)} records")
        print(f"  Availability: {len(delivery_personnel_availability)} records")
        print(f"  Capacity: {len(delivery_personnel_capacity)} records")
        print(f"  Zones: {len(delivery_personnel_zones)} records")
        
        # Check target personnel
        target_id = "dp_31fe5be0"
        if target_id in delivery_personnel:
            print(f"\n👤 Target personnel {target_id} found:")
            personnel_data = delivery_personnel[target_id]
            print(f"  Name: {personnel_data.get('name')}")
            print(f"  Status: {personnel_data.get('status')}")
            print(f"  Verified: {personnel_data.get('is_verified')}")
            print(f"  Service Areas: {personnel_data.get('service_areas')}")
            print(f"  Availability: {delivery_personnel_availability.get(target_id)}")
            print(f"  Capacity: {delivery_personnel_capacity.get(target_id)}")
            print(f"  Zones: {delivery_personnel_zones.get(target_id)}")
        else:
            print(f"\n❌ Target personnel {target_id} NOT found")
        
        print(f"\n🔍 Testing availability for all areas:")
        for area_id in ['1', '2', '3', '4']:
            available = find_available_personnel(area_id)
            print(f"  Area {area_id}: {len(available)} personnel - {available}")
            if target_id in available:
                print(f"    ✅ Target personnel {target_id} is available!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_availability()
