#!/usr/bin/env python3
"""
Debug Management Bot - Comprehensive Callback Debugging
This script will help identify exactly where callback processing is failing
"""

import telebot
import os
import logging
import sys
import time
import threading
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up comprehensive logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_management.log', mode='w')
    ]
)
logger = logging.getLogger(__name__)

# Bot configuration
MANAGEMENT_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017]

def is_authorized(user_id):
    """Check if user is authorized"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

class DebugManagementBot:
    """Debug version of management bot with extensive logging"""
    
    def __init__(self):
        self.bot = None
        self.callback_count = 0
        self.message_count = 0
        
    def start(self):
        """Start the debug management bot"""
        logger.info("🚀 STARTING DEBUG MANAGEMENT BOT")
        logger.info("=" * 60)
        
        if not MANAGEMENT_BOT_TOKEN:
            logger.error("❌ NOTIFICATION_BOT_TOKEN not found")
            return False
        
        logger.info(f"✅ Token loaded (length: {len(MANAGEMENT_BOT_TOKEN)})")
        
        try:
            # Create bot instance
            logger.info("🤖 Creating bot instance...")
            self.bot = telebot.TeleBot(MANAGEMENT_BOT_TOKEN)
            
            # Test connection
            logger.info("🔍 Testing bot connection...")
            bot_info = self.bot.get_me()
            logger.info(f"✅ Bot connected: @{bot_info.username} (ID: {bot_info.id})")
            
            # Clear any existing webhooks
            logger.info("🧹 Clearing webhooks...")
            self.bot.remove_webhook()
            logger.info("✅ Webhooks cleared")
            
            # Register handlers
            self.register_handlers()
            
            # Send startup message
            self.send_startup_message()
            
            # Start polling with debug info
            logger.info("🔄 Starting polling...")
            logger.info("💡 DEBUG MODE: All callback processing will be logged in detail")
            logger.info("📱 Send /menu to test callback functionality")
            logger.info("=" * 60)
            
            # Start polling in a separate thread to allow monitoring
            polling_thread = threading.Thread(target=self.start_polling)
            polling_thread.daemon = True
            polling_thread.start()
            
            # Monitor for activity
            self.monitor_activity()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error starting debug bot: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def start_polling(self):
        """Start bot polling in separate thread"""
        try:
            self.bot.infinity_polling(timeout=10, long_polling_timeout=5)
        except Exception as e:
            logger.error(f"❌ Polling error: {e}")
    
    def monitor_activity(self):
        """Monitor bot activity"""
        logger.info("👁️ Monitoring bot activity...")
        try:
            while True:
                time.sleep(5)
                logger.debug(f"📊 Activity: {self.message_count} messages, {self.callback_count} callbacks processed")
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped by user")
    
    def register_handlers(self):
        """Register all bot handlers with debug logging"""
        logger.info("📝 Registering handlers...")
        
        @self.bot.message_handler(commands=['start'])
        def handle_start(message):
            self.message_count += 1
            user_id = message.from_user.id
            logger.info(f"📨 /start command from user {user_id}")
            
            if not is_authorized(user_id):
                self.bot.reply_to(message, "❌ You are not authorized to use this bot.")
                return
            
            welcome_text = """
🤖 **DEBUG MANAGEMENT BOT**

✅ Bot is running in DEBUG mode
🔍 All callback processing is being logged
📊 Real-time monitoring is active

**Commands:**
• /menu - Test callback functionality
• /status - Check bot status
• /debug - Show debug info

**🎯 This version will show exactly what happens when you click buttons!**
"""
            self.bot.reply_to(message, welcome_text, parse_mode='Markdown')
        
        @self.bot.message_handler(commands=['menu'])
        def handle_menu(message):
            self.message_count += 1
            user_id = message.from_user.id
            logger.info(f"📨 /menu command from user {user_id}")
            
            if not is_authorized(user_id):
                self.bot.reply_to(message, "❌ You are not authorized to use this bot.")
                return
            
            # Create inline keyboard
            markup = telebot.types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                telebot.types.InlineKeyboardButton("👥 Personnel", callback_data="mgmt_personnel"),
                telebot.types.InlineKeyboardButton("📊 Analytics", callback_data="mgmt_analytics")
            )
            markup.add(
                telebot.types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings"),
                telebot.types.InlineKeyboardButton("📈 Performance", callback_data="mgmt_performance")
            )
            markup.add(
                telebot.types.InlineKeyboardButton("🔄 Refresh", callback_data="mgmt_refresh")
            )
            
            menu_text = """
🏢 **DEBUG MANAGEMENT DASHBOARD**

**🔍 DEBUG MODE ACTIVE**

Select a button to test callback functionality:

👥 **Personnel** - Test personnel callback
📊 **Analytics** - Test analytics callback  
💰 **Earnings** - Test earnings callback
📈 **Performance** - Test performance callback
🔄 **Refresh** - Test refresh callback

**📊 Watch the logs for detailed callback processing info!**
"""
            
            logger.info(f"📤 Sending menu with {len(markup.keyboard)} button rows")
            self.bot.reply_to(message, menu_text, reply_markup=markup, parse_mode='Markdown')
        
        @self.bot.message_handler(commands=['status'])
        def handle_status(message):
            self.message_count += 1
            user_id = message.from_user.id
            logger.info(f"📨 /status command from user {user_id}")
            
            if not is_authorized(user_id):
                self.bot.reply_to(message, "❌ You are not authorized to use this bot.")
                return
            
            status_text = f"""
🤖 **DEBUG BOT STATUS**

✅ **Status**: Online and monitoring
📊 **Messages Processed**: {self.message_count}
📞 **Callbacks Processed**: {self.callback_count}
🔍 **Debug Mode**: Active
📝 **Logging**: Comprehensive

**Callback Handler Status**: ✅ Registered and ready
"""
            self.bot.reply_to(message, status_text, parse_mode='Markdown')
        
        # CRITICAL: Callback handler with extensive debugging
        @self.bot.callback_query_handler(func=lambda call: True)
        def handle_callbacks(call):
            """Handle callbacks with extensive debugging"""
            self.callback_count += 1
            user_id = call.from_user.id
            callback_data = call.data
            
            logger.info("=" * 50)
            logger.info(f"🔔 CALLBACK #{self.callback_count} RECEIVED")
            logger.info(f"👤 User ID: {user_id}")
            logger.info(f"📝 Callback Data: '{callback_data}'")
            logger.info(f"💬 Message ID: {call.message.message_id}")
            logger.info(f"💬 Chat ID: {call.message.chat.id}")
            logger.info("=" * 50)
            
            # Step 1: Answer callback query IMMEDIATELY
            logger.info("🔄 STEP 1: Answering callback query...")
            try:
                self.bot.answer_callback_query(call.id)
                logger.info("✅ STEP 1 SUCCESS: Callback query answered")
            except Exception as e:
                logger.error(f"❌ STEP 1 FAILED: Error answering callback query: {e}")
                try:
                    self.bot.answer_callback_query(call.id, "Processing...")
                    logger.info("✅ STEP 1 FALLBACK: Alternative callback answer sent")
                except Exception as e2:
                    logger.error(f"❌ STEP 1 FALLBACK FAILED: {e2}")
            
            # Step 2: Check authorization
            logger.info("🔄 STEP 2: Checking authorization...")
            if not is_authorized(user_id):
                logger.warning(f"❌ STEP 2 FAILED: Unauthorized user {user_id}")
                return
            logger.info("✅ STEP 2 SUCCESS: User authorized")
            
            # Step 3: Process callback
            logger.info("🔄 STEP 3: Processing callback...")
            try:
                if callback_data == "mgmt_personnel":
                    response_text = "👥 **Personnel - DEBUG SUCCESS!**\n\n✅ Callback processed successfully!\n🔍 Check logs for details.\n\nUse /menu to test again."
                elif callback_data == "mgmt_analytics":
                    response_text = "📊 **Analytics - DEBUG SUCCESS!**\n\n✅ Callback processed successfully!\n🔍 Check logs for details.\n\nUse /menu to test again."
                elif callback_data == "mgmt_earnings":
                    response_text = "💰 **Earnings - DEBUG SUCCESS!**\n\n✅ Callback processed successfully!\n🔍 Check logs for details.\n\nUse /menu to test again."
                elif callback_data == "mgmt_performance":
                    response_text = "📈 **Performance - DEBUG SUCCESS!**\n\n✅ Callback processed successfully!\n🔍 Check logs for details.\n\nUse /menu to test again."
                elif callback_data == "mgmt_refresh":
                    response_text = "🔄 **Refresh - DEBUG SUCCESS!**\n\n✅ Callback processed successfully!\n🔍 Check logs for details.\n\nUse /menu to test again."
                else:
                    response_text = f"⚠️ **Unknown Callback**\n\nData: '{callback_data}'\n\nUse /menu to test again."
                
                logger.info(f"✅ STEP 3 SUCCESS: Response prepared ({len(response_text)} chars)")
                
                # Step 4: Edit message
                logger.info("🔄 STEP 4: Editing message...")
                try:
                    self.bot.edit_message_text(
                        response_text,
                        call.message.chat.id,
                        call.message.message_id,
                        parse_mode='Markdown'
                    )
                    logger.info("✅ STEP 4 SUCCESS: Message edited successfully")
                except Exception as edit_error:
                    logger.error(f"❌ STEP 4 FAILED: Error editing message: {edit_error}")
                    # Step 4 Fallback: Send new message
                    logger.info("🔄 STEP 4 FALLBACK: Sending new message...")
                    try:
                        self.bot.send_message(
                            call.message.chat.id,
                            response_text,
                            parse_mode='Markdown'
                        )
                        logger.info("✅ STEP 4 FALLBACK SUCCESS: New message sent")
                    except Exception as send_error:
                        logger.error(f"❌ STEP 4 FALLBACK FAILED: {send_error}")
                
                logger.info(f"🎉 CALLBACK #{self.callback_count} COMPLETED SUCCESSFULLY")
                
            except Exception as e:
                logger.error(f"❌ STEP 3 FAILED: Error processing callback: {e}")
                import traceback
                traceback.print_exc()
            
            logger.info("=" * 50)
        
        logger.info("✅ All handlers registered successfully")
    
    def send_startup_message(self):
        """Send startup message to authorized user"""
        try:
            authorized_id = 7729984017
            startup_message = """
🚀 **DEBUG MANAGEMENT BOT STARTED**

✅ Bot is online and ready for debugging
🔍 Comprehensive logging is active
📊 Real-time callback monitoring enabled

**Ready for testing:**
• Send /menu to test callback functionality
• Watch the console/logs for detailed processing info
• Each button click will be logged step-by-step

**🎯 This will help identify exactly where callback processing fails!**
"""
            
            self.bot.send_message(authorized_id, startup_message, parse_mode='Markdown')
            logger.info("✅ Startup message sent to authorized user")
        except Exception as e:
            logger.error(f"❌ Error sending startup message: {e}")

def main():
    """Main function"""
    debug_bot = DebugManagementBot()
    return debug_bot.start()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("🛑 Debug bot stopped by user")
        sys.exit(0)
