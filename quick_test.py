#!/usr/bin/env python
"""
Quick test to verify bot functionality
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

print("Starting quick test...")

try:
    print("1. Testing config import...")
    from src.config import BOT_TOKEN, logger
    print(f"   ✅ User bot token: {BOT_TOKEN[:10]}...")
    
    print("2. Testing bot instance import...")
    from src.bot_instance import bot
    print("   ✅ Bot instance imported")
    
    print("3. Testing bot connection...")
    bot_info = bot.get_me()
    print(f"   ✅ Bot connected: @{bot_info.username}")
    
    print("4. Testing new bot configs...")
    from src.config import ORDER_TRACK_BOT_TOKEN, DELIVERY_BOT_TOKEN
    print(f"   ✅ Order track token: {ORDER_TRACK_BOT_TOKEN[:10]}...")
    print(f"   ✅ Delivery token: {DELIVERY_BOT_TOKEN[:10]}...")
    
    print("🎉 All tests passed! System is ready.")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
