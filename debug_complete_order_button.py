#!/usr/bin/env python3
"""
Debug script to test the Complete Order button functionality.
This script will help identify why the Complete Order button is not appearing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_order_acceptance_flow():
    """Test the order acceptance flow and Complete Order button"""
    print("🔍 DEBUGGING COMPLETE ORDER BUTTON ISSUE")
    print("=" * 60)
    
    try:
        # Import required modules
        from src.firebase_db import get_data, set_data
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_telegram_id
        from telebot import types
        
        print("✅ All imports successful")
        
        # Check if there are any confirmed orders
        confirmed_orders = get_data("confirmed_orders") or {}
        print(f"📋 Found {len(confirmed_orders)} confirmed orders")
        
        if confirmed_orders:
            # Get the first order for testing
            order_number = list(confirmed_orders.keys())[0]
            order_data = confirmed_orders[order_number]
            print(f"🔍 Testing with order: {order_number}")
            print(f"   Status: {order_data.get('status', 'Unknown')}")
            print(f"   Delivery Status: {order_data.get('delivery_status', 'Unknown')}")
            print(f"   Assigned To: {order_data.get('assigned_to', 'None')}")
            
            # Test Complete Order button creation
            complete_markup = types.InlineKeyboardMarkup()
            complete_btn = types.InlineKeyboardButton(
                "🏁 Complete Order",
                callback_data=f"complete_order_{order_number}"
            )
            complete_markup.add(complete_btn)
            
            print(f"✅ Complete Order button created successfully")
            print(f"   Button text: {complete_btn.text}")
            print(f"   Callback data: {complete_btn.callback_data}")
            print(f"   Callback data length: {len(complete_btn.callback_data)}")
            
            # Test message text creation
            test_message = f"✅ Order #{order_number} ACCEPTED\n\nOrder assigned to you\nClick 'Complete Order' when delivery is finished"
            print(f"✅ Message text created successfully")
            print(f"   Message length: {len(test_message)}")
            
            if len(test_message) > 4096:
                print("❌ Message too long for Telegram!")
            else:
                print("✅ Message length is acceptable")
                
        else:
            print("⚠️  No confirmed orders found for testing")
            
        # Test delivery personnel data
        test_telegram_id = 7729984017  # Known delivery personnel
        personnel = get_delivery_personnel_by_telegram_id(test_telegram_id)
        
        if personnel:
            print(f"✅ Found delivery personnel: {personnel.name}")
            print(f"   Personnel ID: {personnel.personnel_id}")
            print(f"   Telegram ID: {personnel.telegram_id}")
        else:
            print(f"❌ No delivery personnel found for Telegram ID: {test_telegram_id}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error in order acceptance flow test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_data_parsing():
    """Test callback data parsing logic"""
    print("\n🔍 Testing Callback Data Parsing...")
    print("=" * 60)
    
    try:
        # Test different callback data formats
        test_cases = [
            "accept_order_7729984017_2507011425_0001",
            "decline_order_7729984017_2507011425_0001", 
            "complete_order_7729984017_2507011425_0001",
            "accept_7729984017_2507011425_0001",
            "decline_7729984017_2507011425_0001"
        ]
        
        for callback_data in test_cases:
            print(f"Testing: {callback_data}")
            
            # Simulate the parsing logic from delivery bot
            if callback_data.startswith('accept_order_'):
                action = "accept"
                order_number = callback_data.replace('accept_order_', '')
            elif callback_data.startswith('decline_order_'):
                action = "decline"
                order_number = callback_data.replace('decline_order_', '')
            elif callback_data.startswith('complete_order_'):
                action = "complete"
                order_number = callback_data.replace('complete_order_', '')
            else:
                # Old format
                action, order_number = callback_data.split('_', 1)
            
            print(f"   Action: {action}")
            print(f"   Order Number: {order_number}")
            print(f"   ✅ Parsing successful\n")
            
        return True
        
    except Exception as e:
        print(f"❌ Error in callback data parsing test: {e}")
        return False

def test_delivery_bot_imports():
    """Test delivery bot imports and function availability"""
    print("\n🔍 Testing Delivery Bot Imports...")
    print("=" * 60)
    
    try:
        # Test delivery bot import
        from src.bots.delivery_bot import delivery_bot, logger
        print("✅ Delivery bot imported successfully")
        
        # Test if bot is properly initialized
        if hasattr(delivery_bot, 'edit_message_text'):
            print("✅ edit_message_text method available")
        else:
            print("❌ edit_message_text method NOT available")
            
        if hasattr(delivery_bot, 'send_message'):
            print("✅ send_message method available")
        else:
            print("❌ send_message method NOT available")
            
        # Test callback handler registration
        callback_handlers = delivery_bot._callback_query_handlers
        print(f"✅ Found {len(callback_handlers)} callback handlers")
        
        # Test specific handler for complete_order_
        complete_order_handler_found = False
        for handler in callback_handlers:
            if hasattr(handler, 'func') and handler.func:
                # Create mock call
                class MockCall:
                    def __init__(self, data):
                        self.data = data
                
                test_call = MockCall("complete_order_TEST123")
                try:
                    if handler.func(test_call):
                        complete_order_handler_found = True
                        print("✅ Complete order callback handler found")
                        break
                except:
                    pass
        
        if not complete_order_handler_found:
            print("❌ Complete order callback handler NOT found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing delivery bot imports: {e}")
        return False

def main():
    """Run all debug tests"""
    print("🐛 DEBUGGING COMPLETE ORDER BUTTON FUNCTIONALITY")
    print("=" * 80)
    
    tests = [
        test_order_acceptance_flow,
        test_callback_data_parsing,
        test_delivery_bot_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 DEBUG RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All debug tests passed!")
        print("\n🔧 NEXT STEPS:")
        print("1. The Complete Order button logic appears to be correct")
        print("2. Try placing a real order and accepting it to test the actual flow")
        print("3. Check the delivery bot logs for any runtime errors")
        print("4. Verify that the message editing is not failing due to Telegram API issues")
    else:
        print("⚠️  Some debug tests failed. Check the specific errors above.")
        
    print("\n📋 MANUAL TESTING INSTRUCTIONS:")
    print("1. Place an order through the user bot")
    print("2. Approve it through admin and finance bots")
    print("3. Accept the order through delivery bot")
    print("4. Check if the 'Complete Order' button appears")
    print("5. If not, check the delivery bot logs for errors")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
